{"_args": [["loose-envify@1.4.0", "D:\\web\\shenzhen-app"]], "_from": "loose-envify@1.4.0", "_id": "loose-envify@1.4.0", "_inBundle": false, "_integrity": "sha512-lyuxPGr/Wfhrlem2CL/UcnUc1zcqKAImBDzukY7Y5F/yQiNdko6+fRLevlw1HgMySw7f611UIY408EtxRSoK3Q==", "_location": "/loose-envify", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "loose-envify@1.4.0", "name": "loose-envify", "escapedName": "loose-envify", "rawSpec": "1.4.0", "saveSpec": null, "fetchSpec": "1.4.0"}, "_requiredBy": ["/invariant"], "_resolved": "https://registry.npmjs.org/loose-envify/-/loose-envify-1.4.0.tgz", "_spec": "1.4.0", "_where": "D:\\web\\shenzhen-app", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "bin": {"loose-envify": "cli.js"}, "bugs": {"url": "https://github.com/zertosh/loose-envify/issues"}, "dependencies": {"js-tokens": "^3.0.0 || ^4.0.0"}, "description": "Fast (and loose) selective `process.env` replacer using js-tokens instead of an AST", "devDependencies": {"browserify": "^13.1.1", "envify": "^3.4.0", "tap": "^8.0.0"}, "homepage": "https://github.com/zertosh/loose-envify", "keywords": ["environment", "variables", "browserify", "browserify-transform", "transform", "source", "configuration"], "license": "MIT", "main": "index.js", "name": "loose-envify", "repository": {"type": "git", "url": "git://github.com/zertosh/loose-envify.git"}, "scripts": {"test": "tap test/*.js"}, "version": "1.4.0"}