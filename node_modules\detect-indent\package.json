{"_args": [["detect-indent@4.0.0", "D:\\web\\shenzhen-app"]], "_from": "detect-indent@4.0.0", "_id": "detect-indent@4.0.0", "_inBundle": false, "_integrity": "sha1-920GQ1LN9Docts5hnE7jqUdd4gg=", "_location": "/detect-indent", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "detect-indent@4.0.0", "name": "detect-indent", "escapedName": "detect-indent", "rawSpec": "4.0.0", "saveSpec": null, "fetchSpec": "4.0.0"}, "_requiredBy": ["/babel-generator"], "_resolved": "https://registry.npmjs.org/detect-indent/-/detect-indent-4.0.0.tgz", "_spec": "4.0.0", "_where": "D:\\web\\shenzhen-app", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/detect-indent/issues"}, "dependencies": {"repeating": "^2.0.0"}, "description": "Detect the indentation of code", "devDependencies": {"ava": "*", "xo": "*"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/detect-indent#readme", "keywords": ["indent", "indentation", "detect", "infer", "identify", "code", "string", "text", "source", "space", "tab"], "license": "MIT", "name": "detect-indent", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/detect-indent.git"}, "scripts": {"test": "xo && ava"}, "version": "4.0.0", "xo": {"ignores": ["fixture/**"]}}