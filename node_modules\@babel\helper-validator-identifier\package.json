{"_from": "@babel/helper-validator-identifier@^7.27.1", "_id": "@babel/helper-validator-identifier@7.27.1", "_inBundle": false, "_integrity": "sha512-D2hP9eA+Sqx1kBZgzxZh0y1trbuU+JoDkiEwqhQ36nodYqJwyEIhPSdMNd7lOm/4io72luTPWH20Yda0xOuUow==", "_location": "/@babel/helper-validator-identifier", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/helper-validator-identifier@^7.27.1", "name": "@babel/helper-validator-identifier", "escapedName": "@babel%2fhelper-validator-identifier", "scope": "@babel", "rawSpec": "^7.27.1", "saveSpec": null, "fetchSpec": "^7.27.1"}, "_requiredBy": ["/@babel/types"], "_resolved": "https://registry.npmmirror.com/@babel/helper-validator-identifier/-/helper-validator-identifier-7.27.1.tgz", "_shasum": "a7054dcc145a967dd4dc8fee845a57c1316c9df8", "_spec": "@babel/helper-validator-identifier@^7.27.1", "_where": "D:\\web\\shenzhen-app\\node_modules\\@babel\\types", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Validate identifier/keywords name", "devDependencies": {"@unicode/unicode-16.0.0": "^1.0.0", "charcodes": "^0.2.0"}, "engines": {"node": ">=6.9.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "homepage": "https://github.com/babel/babel#readme", "license": "MIT", "main": "./lib/index.js", "name": "@babel/helper-validator-identifier", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-helper-validator-identifier"}, "type": "commonjs", "version": "7.27.1"}