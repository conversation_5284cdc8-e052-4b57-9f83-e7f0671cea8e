{"_args": [["babel-runtime@6.26.0", "D:\\web\\shenzhen-app"]], "_from": "babel-runtime@6.26.0", "_id": "babel-runtime@6.26.0", "_inBundle": false, "_integrity": "sha1-llxwWGaOgrVde/4E/yM3vItWR/4=", "_location": "/babel-runtime", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "babel-runtime@6.26.0", "name": "babel-runtime", "escapedName": "babel-runtime", "rawSpec": "6.26.0", "saveSpec": null, "fetchSpec": "6.26.0"}, "_requiredBy": ["/babel-core", "/babel-generator", "/babel-helpers", "/babel-messages", "/babel-register", "/babel-template", "/babel-traverse", "/babel-types"], "_resolved": "https://registry.npmjs.org/babel-runtime/-/babel-runtime-6.26.0.tgz", "_spec": "6.26.0", "_where": "D:\\web\\shenzhen-app", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "dependencies": {"core-js": "^2.4.0", "regenerator-runtime": "^0.11.0"}, "description": "babel selfContained runtime", "devDependencies": {"babel-helpers": "^6.22.0", "babel-plugin-transform-runtime": "^6.23.0"}, "license": "MIT", "name": "babel-runtime", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-runtime"}, "version": "6.26.0"}