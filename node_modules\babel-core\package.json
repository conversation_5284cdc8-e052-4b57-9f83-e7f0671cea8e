{"_args": [["babel-core@6.26.3", "D:\\web\\shenzhen-app"]], "_from": "babel-core@6.26.3", "_id": "babel-core@6.26.3", "_inBundle": false, "_integrity": "sha512-6jyFLuDmeidKmUEb3NM+/yawG0M2bDZ9Z1qbZP59cyHLz8kYGKYwpJP0UwUKKUiTRNvxfLesJnTedqczP7cTDA==", "_location": "/babel-core", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "babel-core@6.26.3", "name": "babel-core", "escapedName": "babel-core", "rawSpec": "6.26.3", "saveSpec": null, "fetchSpec": "6.26.3"}, "_requiredBy": ["/babel-register"], "_resolved": "https://registry.npmjs.org/babel-core/-/babel-core-6.26.3.tgz", "_spec": "6.26.3", "_where": "D:\\web\\shenzhen-app", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "dependencies": {"babel-code-frame": "^6.26.0", "babel-generator": "^6.26.0", "babel-helpers": "^6.24.1", "babel-messages": "^6.23.0", "babel-register": "^6.26.0", "babel-runtime": "^6.26.0", "babel-template": "^6.26.0", "babel-traverse": "^6.26.0", "babel-types": "^6.26.0", "babylon": "^6.18.0", "convert-source-map": "^1.5.1", "debug": "^2.6.9", "json5": "^0.5.1", "lodash": "^4.17.4", "minimatch": "^3.0.4", "path-is-absolute": "^1.0.1", "private": "^0.1.8", "slash": "^1.0.0", "source-map": "^0.5.7"}, "description": "Babel compiler core.", "devDependencies": {"babel-helper-fixtures": "^6.26.2", "babel-helper-transform-fixture-test-runner": "^6.26.2", "babel-polyfill": "^6.26.0"}, "homepage": "https://babeljs.io/", "keywords": ["6to5", "babel", "classes", "const", "es6", "harmony", "let", "modules", "transpile", "transpiler", "var", "babel-core", "compiler"], "license": "MIT", "name": "babel-core", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-core"}, "scripts": {"bench": "make bench", "test": "make test"}, "version": "6.26.3"}