{"builtin": {"Array": false, "ArrayBuffer": false, "Boolean": false, "constructor": false, "DataView": false, "Date": false, "decodeURI": false, "decodeURIComponent": false, "encodeURI": false, "encodeURIComponent": false, "Error": false, "escape": false, "eval": false, "EvalError": false, "Float32Array": false, "Float64Array": false, "Function": false, "hasOwnProperty": false, "Infinity": false, "Int16Array": false, "Int32Array": false, "Int8Array": false, "isFinite": false, "isNaN": false, "isPrototypeOf": false, "JSON": false, "Map": false, "Math": false, "NaN": false, "Number": false, "Object": false, "parseFloat": false, "parseInt": false, "Promise": false, "propertyIsEnumerable": false, "Proxy": false, "RangeError": false, "ReferenceError": false, "Reflect": false, "RegExp": false, "Set": false, "String": false, "Symbol": false, "SyntaxError": false, "System": false, "toLocaleString": false, "toString": false, "TypeError": false, "Uint16Array": false, "Uint32Array": false, "Uint8Array": false, "Uint8ClampedArray": false, "undefined": false, "unescape": false, "URIError": false, "valueOf": false, "WeakMap": false, "WeakSet": false}, "es5": {"Array": false, "Boolean": false, "constructor": false, "Date": false, "decodeURI": false, "decodeURIComponent": false, "encodeURI": false, "encodeURIComponent": false, "Error": false, "escape": false, "eval": false, "EvalError": false, "Function": false, "hasOwnProperty": false, "Infinity": false, "isFinite": false, "isNaN": false, "isPrototypeOf": false, "JSON": false, "Math": false, "NaN": false, "Number": false, "Object": false, "parseFloat": false, "parseInt": false, "propertyIsEnumerable": false, "RangeError": false, "ReferenceError": false, "RegExp": false, "String": false, "SyntaxError": false, "toLocaleString": false, "toString": false, "TypeError": false, "undefined": false, "unescape": false, "URIError": false, "valueOf": false}, "es6": {"Array": false, "ArrayBuffer": false, "Boolean": false, "constructor": false, "DataView": false, "Date": false, "decodeURI": false, "decodeURIComponent": false, "encodeURI": false, "encodeURIComponent": false, "Error": false, "escape": false, "eval": false, "EvalError": false, "Float32Array": false, "Float64Array": false, "Function": false, "hasOwnProperty": false, "Infinity": false, "Int16Array": false, "Int32Array": false, "Int8Array": false, "isFinite": false, "isNaN": false, "isPrototypeOf": false, "JSON": false, "Map": false, "Math": false, "NaN": false, "Number": false, "Object": false, "parseFloat": false, "parseInt": false, "Promise": false, "propertyIsEnumerable": false, "Proxy": false, "RangeError": false, "ReferenceError": false, "Reflect": false, "RegExp": false, "Set": false, "String": false, "Symbol": false, "SyntaxError": false, "System": false, "toLocaleString": false, "toString": false, "TypeError": false, "Uint16Array": false, "Uint32Array": false, "Uint8Array": false, "Uint8ClampedArray": false, "undefined": false, "unescape": false, "URIError": false, "valueOf": false, "WeakMap": false, "WeakSet": false}, "browser": {"addEventListener": false, "alert": false, "AnalyserNode": false, "Animation": false, "AnimationEffectReadOnly": false, "AnimationEffectTiming": false, "AnimationEffectTimingReadOnly": false, "AnimationEvent": false, "AnimationPlaybackEvent": false, "AnimationTimeline": false, "applicationCache": false, "ApplicationCache": false, "ApplicationCacheErrorEvent": false, "atob": false, "Attr": false, "Audio": false, "AudioBuffer": false, "AudioBufferSourceNode": false, "AudioContext": false, "AudioDestinationNode": false, "AudioListener": false, "AudioNode": false, "AudioParam": false, "AudioProcessingEvent": false, "AutocompleteErrorEvent": false, "BarProp": false, "BatteryManager": false, "BeforeUnloadEvent": false, "BiquadFilterNode": false, "Blob": false, "blur": false, "btoa": false, "Cache": false, "caches": false, "CacheStorage": false, "cancelAnimationFrame": false, "cancelIdleCallback": false, "CanvasGradient": false, "CanvasPattern": false, "CanvasRenderingContext2D": false, "CDATASection": false, "ChannelMergerNode": false, "ChannelSplitterNode": false, "CharacterData": false, "clearInterval": false, "clearTimeout": false, "clientInformation": false, "ClientRect": false, "ClientRectList": false, "ClipboardEvent": false, "close": false, "closed": false, "CloseEvent": false, "Comment": false, "CompositionEvent": false, "confirm": false, "console": false, "ConvolverNode": false, "createImageBitmap": false, "Credential": false, "CredentialsContainer": false, "crypto": false, "Crypto": false, "CryptoKey": false, "CSS": false, "CSSAnimation": false, "CSSFontFaceRule": false, "CSSImportRule": false, "CSSKeyframeRule": false, "CSSKeyframesRule": false, "CSSMediaRule": false, "CSSPageRule": false, "CSSRule": false, "CSSRuleList": false, "CSSStyleDeclaration": false, "CSSStyleRule": false, "CSSStyleSheet": false, "CSSSupportsRule": false, "CSSTransition": false, "CSSUnknownRule": false, "CSSViewportRule": false, "customElements": false, "CustomEvent": false, "DataTransfer": false, "DataTransferItem": false, "DataTransferItemList": false, "Debug": false, "defaultStatus": false, "defaultstatus": false, "DelayNode": false, "DeviceMotionEvent": false, "DeviceOrientationEvent": false, "devicePixelRatio": false, "dispatchEvent": false, "document": false, "Document": false, "DocumentFragment": false, "DocumentTimeline": false, "DocumentType": false, "DOMError": false, "DOMException": false, "DOMImplementation": false, "DOMParser": false, "DOMSettableTokenList": false, "DOMStringList": false, "DOMStringMap": false, "DOMTokenList": false, "DragEvent": false, "DynamicsCompressorNode": false, "Element": false, "ElementTimeControl": false, "ErrorEvent": false, "event": false, "Event": false, "EventSource": false, "EventTarget": false, "external": false, "FederatedCredential": false, "fetch": false, "File": false, "FileError": false, "FileList": false, "FileReader": false, "find": false, "focus": false, "FocusEvent": false, "FontFace": false, "FormData": false, "frameElement": false, "frames": false, "GainNode": false, "Gamepad": false, "GamepadButton": false, "GamepadEvent": false, "getComputedStyle": false, "getSelection": false, "HashChangeEvent": false, "Headers": false, "history": false, "History": false, "HTMLAllCollection": false, "HTMLAnchorElement": false, "HTMLAppletElement": false, "HTMLAreaElement": false, "HTMLAudioElement": false, "HTMLBaseElement": false, "HTMLBlockquoteElement": false, "HTMLBodyElement": false, "HTMLBRElement": false, "HTMLButtonElement": false, "HTMLCanvasElement": false, "HTMLCollection": false, "HTMLContentElement": false, "HTMLDataListElement": false, "HTMLDetailsElement": false, "HTMLDialogElement": false, "HTMLDirectoryElement": false, "HTMLDivElement": false, "HTMLDListElement": false, "HTMLDocument": false, "HTMLElement": false, "HTMLEmbedElement": false, "HTMLFieldSetElement": false, "HTMLFontElement": false, "HTMLFormControlsCollection": false, "HTMLFormElement": false, "HTMLFrameElement": false, "HTMLFrameSetElement": false, "HTMLHeadElement": false, "HTMLHeadingElement": false, "HTMLHRElement": false, "HTMLHtmlElement": false, "HTMLIFrameElement": false, "HTMLImageElement": false, "HTMLInputElement": false, "HTMLIsIndexElement": false, "HTMLKeygenElement": false, "HTMLLabelElement": false, "HTMLLayerElement": false, "HTMLLegendElement": false, "HTMLLIElement": false, "HTMLLinkElement": false, "HTMLMapElement": false, "HTMLMarqueeElement": false, "HTMLMediaElement": false, "HTMLMenuElement": false, "HTMLMetaElement": false, "HTMLMeterElement": false, "HTMLModElement": false, "HTMLObjectElement": false, "HTMLOListElement": false, "HTMLOptGroupElement": false, "HTMLOptionElement": false, "HTMLOptionsCollection": false, "HTMLOutputElement": false, "HTMLParagraphElement": false, "HTMLParamElement": false, "HTMLPictureElement": false, "HTMLPreElement": false, "HTMLProgressElement": false, "HTMLQuoteElement": false, "HTMLScriptElement": false, "HTMLSelectElement": false, "HTMLShadowElement": false, "HTMLSourceElement": false, "HTMLSpanElement": false, "HTMLStyleElement": false, "HTMLTableCaptionElement": false, "HTMLTableCellElement": false, "HTMLTableColElement": false, "HTMLTableElement": false, "HTMLTableRowElement": false, "HTMLTableSectionElement": false, "HTMLTemplateElement": false, "HTMLTextAreaElement": false, "HTMLTitleElement": false, "HTMLTrackElement": false, "HTMLUListElement": false, "HTMLUnknownElement": false, "HTMLVideoElement": false, "IDBCursor": false, "IDBCursorWithValue": false, "IDBDatabase": false, "IDBEnvironment": false, "IDBFactory": false, "IDBIndex": false, "IDBKeyRange": false, "IDBObjectStore": false, "IDBOpenDBRequest": false, "IDBRequest": false, "IDBTransaction": false, "IDBVersionChangeEvent": false, "Image": false, "ImageBitmap": false, "ImageData": false, "indexedDB": false, "innerHeight": false, "innerWidth": false, "InputEvent": false, "InputMethodContext": false, "IntersectionObserver": false, "IntersectionObserverEntry": false, "Intl": false, "KeyboardEvent": false, "KeyframeEffect": false, "KeyframeEffectReadOnly": false, "length": false, "localStorage": false, "location": false, "Location": false, "locationbar": false, "matchMedia": false, "MediaElementAudioSourceNode": false, "MediaEncryptedEvent": false, "MediaError": false, "MediaKeyError": false, "MediaKeyEvent": false, "MediaKeyMessageEvent": false, "MediaKeys": false, "MediaKeySession": false, "MediaKeyStatusMap": false, "MediaKeySystemAccess": false, "MediaList": false, "MediaQueryList": false, "MediaQueryListEvent": false, "MediaSource": false, "MediaRecorder": false, "MediaStream": false, "MediaStreamAudioDestinationNode": false, "MediaStreamAudioSourceNode": false, "MediaStreamEvent": false, "MediaStreamTrack": false, "menubar": false, "MessageChannel": false, "MessageEvent": false, "MessagePort": false, "MIDIAccess": false, "MIDIConnectionEvent": false, "MIDIInput": false, "MIDIInputMap": false, "MIDIMessageEvent": false, "MIDIOutput": false, "MIDIOutputMap": false, "MIDIPort": false, "MimeType": false, "MimeTypeArray": false, "MouseEvent": false, "moveBy": false, "moveTo": false, "MutationEvent": false, "MutationObserver": false, "MutationRecord": false, "name": false, "NamedNodeMap": false, "navigator": false, "Navigator": false, "Node": false, "NodeFilter": false, "NodeIterator": false, "NodeList": false, "Notification": false, "OfflineAudioCompletionEvent": false, "OfflineAudioContext": false, "offscreenBuffering": false, "onbeforeunload": true, "onblur": true, "onerror": true, "onfocus": true, "onload": true, "onresize": true, "onunload": true, "open": false, "openDatabase": false, "opener": false, "opera": false, "Option": false, "OscillatorNode": false, "outerHeight": false, "outerWidth": false, "PageTransitionEvent": false, "pageXOffset": false, "pageYOffset": false, "parent": false, "PasswordCredential": false, "Path2D": false, "performance": false, "Performance": false, "PerformanceEntry": false, "PerformanceMark": false, "PerformanceMeasure": false, "PerformanceNavigation": false, "PerformanceResourceTiming": false, "PerformanceTiming": false, "PeriodicWave": false, "Permissions": false, "PermissionStatus": false, "personalbar": false, "Plugin": false, "PluginArray": false, "PopStateEvent": false, "postMessage": false, "print": false, "ProcessingInstruction": false, "ProgressEvent": false, "PromiseRejectionEvent": false, "prompt": false, "PushManager": false, "PushSubscription": false, "RadioNodeList": false, "Range": false, "ReadableByteStream": false, "ReadableStream": false, "removeEventListener": false, "Request": false, "requestAnimationFrame": false, "requestIdleCallback": false, "resizeBy": false, "resizeTo": false, "Response": false, "RTCIceCandidate": false, "RTCSessionDescription": false, "RTCPeerConnection": false, "screen": false, "Screen": false, "screenLeft": false, "ScreenOrientation": false, "screenTop": false, "screenX": false, "screenY": false, "ScriptProcessorNode": false, "scroll": false, "scrollbars": false, "scrollBy": false, "scrollTo": false, "scrollX": false, "scrollY": false, "SecurityPolicyViolationEvent": false, "Selection": false, "self": false, "ServiceWorker": false, "ServiceWorkerContainer": false, "ServiceWorkerRegistration": false, "sessionStorage": false, "setInterval": false, "setTimeout": false, "ShadowRoot": false, "SharedKeyframeList": false, "SharedWorker": false, "showModalDialog": false, "SiteBoundCredential": false, "speechSynthesis": false, "SpeechSynthesisEvent": false, "SpeechSynthesisUtterance": false, "status": false, "statusbar": false, "stop": false, "Storage": false, "StorageEvent": false, "styleMedia": false, "StyleSheet": false, "StyleSheetList": false, "SubtleCrypto": false, "SVGAElement": false, "SVGAltGlyphDefElement": false, "SVGAltGlyphElement": false, "SVGAltGlyphItemElement": false, "SVGAngle": false, "SVGAnimateColorElement": false, "SVGAnimatedAngle": false, "SVGAnimatedBoolean": false, "SVGAnimatedEnumeration": false, "SVGAnimatedInteger": false, "SVGAnimatedLength": false, "SVGAnimatedLengthList": false, "SVGAnimatedNumber": false, "SVGAnimatedNumberList": false, "SVGAnimatedPathData": false, "SVGAnimatedPoints": false, "SVGAnimatedPreserveAspectRatio": false, "SVGAnimatedRect": false, "SVGAnimatedString": false, "SVGAnimatedTransformList": false, "SVGAnimateElement": false, "SVGAnimateMotionElement": false, "SVGAnimateTransformElement": false, "SVGAnimationElement": false, "SVGCircleElement": false, "SVGClipPathElement": false, "SVGColor": false, "SVGColorProfileElement": false, "SVGColorProfileRule": false, "SVGComponentTransferFunctionElement": false, "SVGCSSRule": false, "SVGCursorElement": false, "SVGDefsElement": false, "SVGDescElement": false, "SVGDiscardElement": false, "SVGDocument": false, "SVGElement": false, "SVGElementInstance": false, "SVGElementInstanceList": false, "SVGEllipseElement": false, "SVGEvent": false, "SVGExternalResourcesRequired": false, "SVGFEBlendElement": false, "SVGFEColorMatrixElement": false, "SVGFEComponentTransferElement": false, "SVGFECompositeElement": false, "SVGFEConvolveMatrixElement": false, "SVGFEDiffuseLightingElement": false, "SVGFEDisplacementMapElement": false, "SVGFEDistantLightElement": false, "SVGFEDropShadowElement": false, "SVGFEFloodElement": false, "SVGFEFuncAElement": false, "SVGFEFuncBElement": false, "SVGFEFuncGElement": false, "SVGFEFuncRElement": false, "SVGFEGaussianBlurElement": false, "SVGFEImageElement": false, "SVGFEMergeElement": false, "SVGFEMergeNodeElement": false, "SVGFEMorphologyElement": false, "SVGFEOffsetElement": false, "SVGFEPointLightElement": false, "SVGFESpecularLightingElement": false, "SVGFESpotLightElement": false, "SVGFETileElement": false, "SVGFETurbulenceElement": false, "SVGFilterElement": false, "SVGFilterPrimitiveStandardAttributes": false, "SVGFitToViewBox": false, "SVGFontElement": false, "SVGFontFaceElement": false, "SVGFontFaceFormatElement": false, "SVGFontFaceNameElement": false, "SVGFontFaceSrcElement": false, "SVGFontFaceUriElement": false, "SVGForeignObjectElement": false, "SVGGElement": false, "SVGGeometryElement": false, "SVGGlyphElement": false, "SVGGlyphRefElement": false, "SVGGradientElement": false, "SVGGraphicsElement": false, "SVGHKernElement": false, "SVGICCColor": false, "SVGImageElement": false, "SVGLangSpace": false, "SVGLength": false, "SVGLengthList": false, "SVGLinearGradientElement": false, "SVGLineElement": false, "SVGLocatable": false, "SVGMarkerElement": false, "SVGMaskElement": false, "SVGMatrix": false, "SVGMetadataElement": false, "SVGMissingGlyphElement": false, "SVGMPathElement": false, "SVGNumber": false, "SVGNumberList": false, "SVGPaint": false, "SVGPathElement": false, "SVGPathSeg": false, "SVGPathSegArcAbs": false, "SVGPathSegArcRel": false, "SVGPathSegClosePath": false, "SVGPathSegCurvetoCubicAbs": false, "SVGPathSegCurvetoCubicRel": false, "SVGPathSegCurvetoCubicSmoothAbs": false, "SVGPathSegCurvetoCubicSmoothRel": false, "SVGPathSegCurvetoQuadraticAbs": false, "SVGPathSegCurvetoQuadraticRel": false, "SVGPathSegCurvetoQuadraticSmoothAbs": false, "SVGPathSegCurvetoQuadraticSmoothRel": false, "SVGPathSegLinetoAbs": false, "SVGPathSegLinetoHorizontalAbs": false, "SVGPathSegLinetoHorizontalRel": false, "SVGPathSegLinetoRel": false, "SVGPathSegLinetoVerticalAbs": false, "SVGPathSegLinetoVerticalRel": false, "SVGPathSegList": false, "SVGPathSegMovetoAbs": false, "SVGPathSegMovetoRel": false, "SVGPatternElement": false, "SVGPoint": false, "SVGPointList": false, "SVGPolygonElement": false, "SVGPolylineElement": false, "SVGPreserveAspectRatio": false, "SVGRadialGradientElement": false, "SVGRect": false, "SVGRectElement": false, "SVGRenderingIntent": false, "SVGScriptElement": false, "SVGSetElement": false, "SVGStopElement": false, "SVGStringList": false, "SVGStylable": false, "SVGStyleElement": false, "SVGSVGElement": false, "SVGSwitchElement": false, "SVGSymbolElement": false, "SVGTests": false, "SVGTextContentElement": false, "SVGTextElement": false, "SVGTextPathElement": false, "SVGTextPositioningElement": false, "SVGTitleElement": false, "SVGTransform": false, "SVGTransformable": false, "SVGTransformList": false, "SVGTRefElement": false, "SVGTSpanElement": false, "SVGUnitTypes": false, "SVGURIReference": false, "SVGUseElement": false, "SVGViewElement": false, "SVGViewSpec": false, "SVGVKernElement": false, "SVGZoomAndPan": false, "SVGZoomEvent": false, "Text": false, "TextDecoder": false, "TextEncoder": false, "TextEvent": false, "TextMetrics": false, "TextTrack": false, "TextTrackCue": false, "TextTrackCueList": false, "TextTrackList": false, "TimeEvent": false, "TimeRanges": false, "toolbar": false, "top": false, "Touch": false, "TouchEvent": false, "TouchList": false, "TrackEvent": false, "TransitionEvent": false, "TreeWalker": false, "UIEvent": false, "URL": false, "URLSearchParams": false, "ValidityState": false, "VTTCue": false, "WaveShaperNode": false, "WebGLActiveInfo": false, "WebGLBuffer": false, "WebGLContextEvent": false, "WebGLFramebuffer": false, "WebGLProgram": false, "WebGLRenderbuffer": false, "WebGLRenderingContext": false, "WebGLShader": false, "WebGLShaderPrecisionFormat": false, "WebGLTexture": false, "WebGLUniformLocation": false, "WebSocket": false, "WheelEvent": false, "window": false, "Window": false, "Worker": false, "XDomainRequest": false, "XMLDocument": false, "XMLHttpRequest": false, "XMLHttpRequestEventTarget": false, "XMLHttpRequestProgressEvent": false, "XMLHttpRequestUpload": false, "XMLSerializer": false, "XPathEvaluator": false, "XPathException": false, "XPathExpression": false, "XPathNamespace": false, "XPathNSResolver": false, "XPathResult": false, "XSLTProcessor": false}, "worker": {"applicationCache": false, "atob": false, "Blob": false, "BroadcastChannel": false, "btoa": false, "Cache": false, "caches": false, "clearInterval": false, "clearTimeout": false, "close": true, "console": false, "fetch": false, "FileReaderSync": false, "FormData": false, "Headers": false, "IDBCursor": false, "IDBCursorWithValue": false, "IDBDatabase": false, "IDBFactory": false, "IDBIndex": false, "IDBKeyRange": false, "IDBObjectStore": false, "IDBOpenDBRequest": false, "IDBRequest": false, "IDBTransaction": false, "IDBVersionChangeEvent": false, "ImageData": false, "importScripts": true, "indexedDB": false, "location": false, "MessageChannel": false, "MessagePort": false, "name": false, "navigator": false, "Notification": false, "onclose": true, "onconnect": true, "onerror": true, "onlanguagechange": true, "onmessage": true, "onoffline": true, "ononline": true, "onrejectionhandled": true, "onunhandledrejection": true, "performance": false, "Performance": false, "PerformanceEntry": false, "PerformanceMark": false, "PerformanceMeasure": false, "PerformanceNavigation": false, "PerformanceResourceTiming": false, "PerformanceTiming": false, "postMessage": true, "Promise": false, "Request": false, "Response": false, "self": true, "ServiceWorkerRegistration": false, "setInterval": false, "setTimeout": false, "TextDecoder": false, "TextEncoder": false, "URL": false, "URLSearchParams": false, "WebSocket": false, "Worker": false, "XMLHttpRequest": false}, "node": {"__dirname": false, "__filename": false, "arguments": false, "Buffer": false, "clearImmediate": false, "clearInterval": false, "clearTimeout": false, "console": false, "exports": true, "GLOBAL": false, "global": false, "Intl": false, "module": false, "process": false, "require": false, "root": false, "setImmediate": false, "setInterval": false, "setTimeout": false}, "commonjs": {"exports": true, "module": false, "require": false, "global": false}, "amd": {"define": false, "require": false}, "mocha": {"after": false, "afterEach": false, "before": false, "beforeEach": false, "context": false, "describe": false, "it": false, "mocha": false, "run": false, "setup": false, "specify": false, "suite": false, "suiteSetup": false, "suiteTeardown": false, "teardown": false, "test": false, "xcontext": false, "xdescribe": false, "xit": false, "xspecify": false}, "jasmine": {"afterAll": false, "afterEach": false, "beforeAll": false, "beforeEach": false, "describe": false, "expect": false, "fail": false, "fdescribe": false, "fit": false, "it": false, "jasmine": false, "pending": false, "runs": false, "spyOn": false, "spyOnProperty": false, "waits": false, "waitsFor": false, "xdescribe": false, "xit": false}, "jest": {"afterAll": false, "afterEach": false, "beforeAll": false, "beforeEach": false, "check": false, "describe": false, "expect": false, "gen": false, "it": false, "fdescribe": false, "fit": false, "jest": false, "pit": false, "require": false, "test": false, "xdescribe": false, "xit": false, "xtest": false}, "qunit": {"asyncTest": false, "deepEqual": false, "equal": false, "expect": false, "module": false, "notDeepEqual": false, "notEqual": false, "notOk": false, "notPropEqual": false, "notStrictEqual": false, "ok": false, "propEqual": false, "QUnit": false, "raises": false, "start": false, "stop": false, "strictEqual": false, "test": false, "throws": false}, "phantomjs": {"console": true, "exports": true, "phantom": true, "require": true, "WebPage": true}, "couch": {"emit": false, "exports": false, "getRow": false, "log": false, "module": false, "provides": false, "require": false, "respond": false, "send": false, "start": false, "sum": false}, "rhino": {"defineClass": false, "deserialize": false, "gc": false, "help": false, "importClass": false, "importPackage": false, "java": false, "load": false, "loadClass": false, "Packages": false, "print": false, "quit": false, "readFile": false, "readUrl": false, "runCommand": false, "seal": false, "serialize": false, "spawn": false, "sync": false, "toint32": false, "version": false}, "nashorn": {"__DIR__": false, "__FILE__": false, "__LINE__": false, "com": false, "edu": false, "exit": false, "Java": false, "java": false, "javafx": false, "JavaImporter": false, "javax": false, "JSAdapter": false, "load": false, "loadWithNewGlobal": false, "org": false, "Packages": false, "print": false, "quit": false}, "wsh": {"ActiveXObject": true, "Enumerator": true, "GetObject": true, "ScriptEngine": true, "ScriptEngineBuildVersion": true, "ScriptEngineMajorVersion": true, "ScriptEngineMinorVersion": true, "VBArray": true, "WScript": true, "WSH": true, "XDomainRequest": true}, "jquery": {"$": false, "jQuery": false}, "yui": {"Y": false, "YUI": false, "YUI_config": false}, "shelljs": {"cat": false, "cd": false, "chmod": false, "config": false, "cp": false, "dirs": false, "echo": false, "env": false, "error": false, "exec": false, "exit": false, "find": false, "grep": false, "ls": false, "ln": false, "mkdir": false, "mv": false, "popd": false, "pushd": false, "pwd": false, "rm": false, "sed": false, "set": false, "target": false, "tempdir": false, "test": false, "touch": false, "which": false}, "prototypejs": {"$": false, "$$": false, "$A": false, "$break": false, "$continue": false, "$F": false, "$H": false, "$R": false, "$w": false, "Abstract": false, "Ajax": false, "Autocompleter": false, "Builder": false, "Class": false, "Control": false, "Draggable": false, "Draggables": false, "Droppables": false, "Effect": false, "Element": false, "Enumerable": false, "Event": false, "Field": false, "Form": false, "Hash": false, "Insertion": false, "ObjectRange": false, "PeriodicalExecuter": false, "Position": false, "Prototype": false, "Scriptaculous": false, "Selector": false, "Sortable": false, "SortableObserver": false, "Sound": false, "Template": false, "Toggle": false, "Try": false}, "meteor": {"$": false, "_": false, "Accounts": false, "AccountsClient": false, "AccountsServer": false, "AccountsCommon": false, "App": false, "Assets": false, "Blaze": false, "check": false, "Cordova": false, "DDP": false, "DDPServer": false, "DDPRateLimiter": false, "Deps": false, "EJSON": false, "Email": false, "HTTP": false, "Log": false, "Match": false, "Meteor": false, "Mongo": false, "MongoInternals": false, "Npm": false, "Package": false, "Plugin": false, "process": false, "Random": false, "ReactiveDict": false, "ReactiveVar": false, "Router": false, "ServiceConfiguration": false, "Session": false, "share": false, "Spacebars": false, "Template": false, "Tinytest": false, "Tracker": false, "UI": false, "Utils": false, "WebApp": false, "WebAppInternals": false}, "mongo": {"_isWindows": false, "_rand": false, "BulkWriteResult": false, "cat": false, "cd": false, "connect": false, "db": false, "getHostName": false, "getMemInfo": false, "hostname": false, "ISODate": false, "listFiles": false, "load": false, "ls": false, "md5sumFile": false, "mkdir": false, "Mongo": false, "NumberInt": false, "NumberLong": false, "ObjectId": false, "PlanCache": false, "print": false, "printjson": false, "pwd": false, "quit": false, "removeFile": false, "rs": false, "sh": false, "UUID": false, "version": false, "WriteResult": false}, "applescript": {"$": false, "Application": false, "Automation": false, "console": false, "delay": false, "Library": false, "ObjC": false, "ObjectSpecifier": false, "Path": false, "Progress": false, "Ref": false}, "serviceworker": {"caches": false, "Cache": false, "CacheStorage": false, "Client": false, "clients": false, "Clients": false, "ExtendableEvent": false, "ExtendableMessageEvent": false, "FetchEvent": false, "importScripts": false, "registration": false, "self": false, "ServiceWorker": false, "ServiceWorkerContainer": false, "ServiceWorkerGlobalScope": false, "ServiceWorkerMessageEvent": false, "ServiceWorkerRegistration": false, "skipWaiting": false, "WindowClient": false}, "atomtest": {"advanceClock": false, "fakeClearInterval": false, "fakeClearTimeout": false, "fakeSetInterval": false, "fakeSetTimeout": false, "resetTimeouts": false, "waitsForPromise": false}, "embertest": {"andThen": false, "click": false, "currentPath": false, "currentRouteName": false, "currentURL": false, "fillIn": false, "find": false, "findWithAssert": false, "keyEvent": false, "pauseTest": false, "resumeTest": false, "triggerEvent": false, "visit": false}, "protractor": {"$": false, "$$": false, "browser": false, "By": false, "by": false, "DartObject": false, "element": false, "protractor": false}, "shared-node-browser": {"clearInterval": false, "clearTimeout": false, "console": false, "setInterval": false, "setTimeout": false}, "webextensions": {"browser": false, "chrome": false, "opr": false}, "greasemonkey": {"GM_addStyle": false, "GM_deleteValue": false, "GM_getResourceText": false, "GM_getResourceURL": false, "GM_getValue": false, "GM_info": false, "GM_listValues": false, "GM_log": false, "GM_openInTab": false, "GM_registerMenuCommand": false, "GM_setClipboard": false, "GM_setValue": false, "GM_xmlhttpRequest": false, "unsafeWindow": false}}