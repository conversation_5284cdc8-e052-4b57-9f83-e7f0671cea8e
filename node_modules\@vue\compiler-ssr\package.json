{"_from": "@vue/compiler-ssr@3.5.16", "_id": "@vue/compiler-ssr@3.5.16", "_inBundle": false, "_integrity": "sha512-d2V7kfxbdsjrDSGlJE7my1ZzCXViEcqN6w14DOsDrUCHEA6vbnVCpRFfrc4ryCP/lCKzX2eS1YtnLE/BuC9f/A==", "_location": "/@vue/compiler-ssr", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@vue/compiler-ssr@3.5.16", "name": "@vue/compiler-ssr", "escapedName": "@vue%2fcompiler-ssr", "scope": "@vue", "rawSpec": "3.5.16", "saveSpec": null, "fetchSpec": "3.5.16"}, "_requiredBy": ["/@vue/compiler-sfc", "/@vue/server-renderer"], "_resolved": "https://registry.npmmirror.com/@vue/compiler-ssr/-/compiler-ssr-3.5.16.tgz", "_shasum": "3b7874dff771ab2f85fb09be71f6c76a75fcc5ac", "_spec": "@vue/compiler-ssr@3.5.16", "_where": "D:\\web\\shenzhen-app\\node_modules\\@vue\\compiler-sfc", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/vuejs/core/issues"}, "buildOptions": {"prod": false, "formats": ["cjs"]}, "bundleDependencies": false, "dependencies": {"@vue/compiler-dom": "3.5.16", "@vue/shared": "3.5.16"}, "deprecated": false, "description": "@vue/compiler-ssr", "files": ["dist"], "homepage": "https://github.com/vuejs/core/tree/main/packages/compiler-ssr#readme", "keywords": ["vue"], "license": "MIT", "main": "dist/compiler-ssr.cjs.js", "name": "@vue/compiler-ssr", "repository": {"type": "git", "url": "git+https://github.com/vuejs/core.git", "directory": "packages/compiler-ssr"}, "types": "dist/compiler-ssr.d.ts", "version": "3.5.16"}