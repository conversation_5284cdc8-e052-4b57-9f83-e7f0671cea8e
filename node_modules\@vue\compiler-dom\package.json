{"_from": "@vue/compiler-dom@3.5.16", "_id": "@vue/compiler-dom@3.5.16", "_inBundle": false, "_integrity": "sha512-SSJIhBr/teipXiXjmWOVWLnxjNGo65Oj/8wTEQz0nqwQeP75jWZ0n4sF24Zxoht1cuJoWopwj0J0exYwCJ0dCQ==", "_location": "/@vue/compiler-dom", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@vue/compiler-dom@3.5.16", "name": "@vue/compiler-dom", "escapedName": "@vue%2fcompiler-dom", "scope": "@vue", "rawSpec": "3.5.16", "saveSpec": null, "fetchSpec": "3.5.16"}, "_requiredBy": ["/@vue/compiler-sfc", "/@vue/compiler-ssr", "/vue"], "_resolved": "https://registry.npmmirror.com/@vue/compiler-dom/-/compiler-dom-3.5.16.tgz", "_shasum": "151d8390252975c0b1a773029220fdfcfaa2d743", "_spec": "@vue/compiler-dom@3.5.16", "_where": "D:\\web\\shenzhen-app\\node_modules\\vue", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/vuejs/core/issues"}, "buildOptions": {"name": "VueCompilerDOM", "compat": true, "formats": ["esm-bundler", "esm-browser", "cjs", "global"]}, "bundleDependencies": false, "dependencies": {"@vue/compiler-core": "3.5.16", "@vue/shared": "3.5.16"}, "deprecated": false, "description": "@vue/compiler-dom", "exports": {".": {"types": "./dist/compiler-dom.d.ts", "node": {"production": "./dist/compiler-dom.cjs.prod.js", "development": "./dist/compiler-dom.cjs.js", "default": "./index.js"}, "module": "./dist/compiler-dom.esm-bundler.js", "import": "./dist/compiler-dom.esm-bundler.js", "require": "./index.js"}, "./*": "./*"}, "files": ["index.js", "dist"], "homepage": "https://github.com/vuejs/core/tree/main/packages/compiler-dom#readme", "jsdelivr": "dist/compiler-dom.global.js", "keywords": ["vue"], "license": "MIT", "main": "index.js", "module": "dist/compiler-dom.esm-bundler.js", "name": "@vue/compiler-dom", "repository": {"type": "git", "url": "git+https://github.com/vuejs/core.git", "directory": "packages/compiler-dom"}, "sideEffects": false, "types": "dist/compiler-dom.d.ts", "unpkg": "dist/compiler-dom.global.js", "version": "3.5.16"}