{"_args": [["babel-messages@6.23.0", "D:\\web\\shenzhen-app"]], "_from": "babel-messages@6.23.0", "_id": "babel-messages@6.23.0", "_inBundle": false, "_integrity": "sha1-8830cDhYA1sqKVHG7F7fbGLyYw4=", "_location": "/babel-messages", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "babel-messages@6.23.0", "name": "babel-messages", "escapedName": "babel-messages", "rawSpec": "6.23.0", "saveSpec": null, "fetchSpec": "6.23.0"}, "_requiredBy": ["/babel-core", "/babel-generator", "/babel-traverse"], "_resolved": "https://registry.npmjs.org/babel-messages/-/babel-messages-6.23.0.tgz", "_spec": "6.23.0", "_where": "D:\\web\\shenzhen-app", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "dependencies": {"babel-runtime": "^6.22.0"}, "description": "Collection of debug messages used by Babel.", "homepage": "https://babeljs.io/", "license": "MIT", "main": "lib/index.js", "name": "babel-messages", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-messages"}, "version": "6.23.0"}