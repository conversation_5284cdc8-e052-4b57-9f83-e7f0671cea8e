{"_from": "@vue/compiler-core@3.5.16", "_id": "@vue/compiler-core@3.5.16", "_inBundle": false, "_integrity": "sha512-AOQS2eaQOaaZQoL1u+2rCJIKDruNXVBZSiUD3chnUrsoX5ZTQMaCvXlWNIfxBJuU15r1o7+mpo5223KVtIhAgQ==", "_location": "/@vue/compiler-core", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@vue/compiler-core@3.5.16", "name": "@vue/compiler-core", "escapedName": "@vue%2fcompiler-core", "scope": "@vue", "rawSpec": "3.5.16", "saveSpec": null, "fetchSpec": "3.5.16"}, "_requiredBy": ["/@vue/compiler-dom", "/@vue/compiler-sfc"], "_resolved": "https://registry.npmmirror.com/@vue/compiler-core/-/compiler-core-3.5.16.tgz", "_shasum": "2f95f4f17c16c09c57bbf64399075b921506630b", "_spec": "@vue/compiler-core@3.5.16", "_where": "D:\\web\\shenzhen-app\\node_modules\\@vue\\compiler-dom", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/vuejs/core/issues"}, "buildOptions": {"name": "VueCompilerCore", "compat": true, "formats": ["esm-bundler", "cjs"]}, "bundleDependencies": false, "dependencies": {"@babel/parser": "^7.27.2", "@vue/shared": "3.5.16", "entities": "^4.5.0", "estree-walker": "^2.0.2", "source-map-js": "^1.2.1"}, "deprecated": false, "description": "@vue/compiler-core", "devDependencies": {"@babel/types": "^7.27.1"}, "exports": {".": {"types": "./dist/compiler-core.d.ts", "node": {"production": "./dist/compiler-core.cjs.prod.js", "development": "./dist/compiler-core.cjs.js", "default": "./index.js"}, "module": "./dist/compiler-core.esm-bundler.js", "import": "./dist/compiler-core.esm-bundler.js", "require": "./index.js"}, "./*": "./*"}, "files": ["index.js", "dist"], "homepage": "https://github.com/vuejs/core/tree/main/packages/compiler-core#readme", "keywords": ["vue"], "license": "MIT", "main": "index.js", "module": "dist/compiler-core.esm-bundler.js", "name": "@vue/compiler-core", "repository": {"type": "git", "url": "git+https://github.com/vuejs/core.git", "directory": "packages/compiler-core"}, "types": "dist/compiler-core.d.ts", "version": "3.5.16"}