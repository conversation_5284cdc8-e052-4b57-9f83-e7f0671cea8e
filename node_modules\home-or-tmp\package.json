{"_args": [["home-or-tmp@2.0.0", "D:\\web\\shenzhen-app"]], "_from": "home-or-tmp@2.0.0", "_id": "home-or-tmp@2.0.0", "_inBundle": false, "_integrity": "sha1-42w/LSyufXRqhX440Y1fMqeILbg=", "_location": "/home-or-tmp", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "home-or-tmp@2.0.0", "name": "home-or-tmp", "escapedName": "home-or-tmp", "rawSpec": "2.0.0", "saveSpec": null, "fetchSpec": "2.0.0"}, "_requiredBy": ["/babel-register"], "_resolved": "https://registry.npmjs.org/home-or-tmp/-/home-or-tmp-2.0.0.tgz", "_spec": "2.0.0", "_where": "D:\\web\\shenzhen-app", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/home-or-tmp/issues"}, "dependencies": {"os-homedir": "^1.0.0", "os-tmpdir": "^1.0.1"}, "description": "Get the user home directory with fallback to the system temp directory", "devDependencies": {"ava": "0.0.4"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/home-or-tmp#readme", "keywords": ["user", "home", "homedir", "dir", "directory", "folder", "path", "tmp", "temp", "temporary", "fallback", "graceful", "userprofile"], "license": "MIT", "name": "home-or-tmp", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/home-or-tmp.git"}, "scripts": {"test": "node test.js"}, "version": "2.0.0"}