{"_from": "@vue/runtime-core@3.5.16", "_id": "@vue/runtime-core@3.5.16", "_inBundle": false, "_integrity": "sha512-bw5Ykq6+JFHYxrQa7Tjr+VSzw7Dj4ldR/udyBZbq73fCdJmyy5MPIFR9IX/M5Qs+TtTjuyUTCnmK3lWWwpAcFQ==", "_location": "/@vue/runtime-core", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@vue/runtime-core@3.5.16", "name": "@vue/runtime-core", "escapedName": "@vue%2fruntime-core", "scope": "@vue", "rawSpec": "3.5.16", "saveSpec": null, "fetchSpec": "3.5.16"}, "_requiredBy": ["/@vue/runtime-dom"], "_resolved": "https://registry.npmmirror.com/@vue/runtime-core/-/runtime-core-3.5.16.tgz", "_shasum": "0a828c322224ada26f81a2e227c3d4aebcb72c7a", "_spec": "@vue/runtime-core@3.5.16", "_where": "D:\\web\\shenzhen-app\\node_modules\\@vue\\runtime-dom", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/vuejs/core/issues"}, "buildOptions": {"name": "VueRuntimeCore", "formats": ["esm-bundler", "cjs"]}, "bundleDependencies": false, "dependencies": {"@vue/reactivity": "3.5.16", "@vue/shared": "3.5.16"}, "deprecated": false, "description": "@vue/runtime-core", "exports": {".": {"types": "./dist/runtime-core.d.ts", "node": {"production": "./dist/runtime-core.cjs.prod.js", "development": "./dist/runtime-core.cjs.js", "default": "./index.js"}, "module": "./dist/runtime-core.esm-bundler.js", "import": "./dist/runtime-core.esm-bundler.js", "require": "./index.js"}, "./*": "./*"}, "files": ["index.js", "dist"], "homepage": "https://github.com/vuejs/core/tree/main/packages/runtime-core#readme", "keywords": ["vue"], "license": "MIT", "main": "index.js", "module": "dist/runtime-core.esm-bundler.js", "name": "@vue/runtime-core", "repository": {"type": "git", "url": "git+https://github.com/vuejs/core.git", "directory": "packages/runtime-core"}, "sideEffects": false, "types": "dist/runtime-core.d.ts", "version": "3.5.16"}