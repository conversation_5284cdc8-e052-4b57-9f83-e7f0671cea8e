{"_args": [["babel-helpers@6.24.1", "D:\\web\\shenzhen-app"]], "_from": "babel-helpers@6.24.1", "_id": "babel-helpers@6.24.1", "_inBundle": false, "_integrity": "sha1-NHHenK7DiOXIUOWX5Yom3fN2ArI=", "_location": "/babel-helpers", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "babel-helpers@6.24.1", "name": "babel-helpers", "escapedName": "babel-helpers", "rawSpec": "6.24.1", "saveSpec": null, "fetchSpec": "6.24.1"}, "_requiredBy": ["/babel-core"], "_resolved": "https://registry.npmjs.org/babel-helpers/-/babel-helpers-6.24.1.tgz", "_spec": "6.24.1", "_where": "D:\\web\\shenzhen-app", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "dependencies": {"babel-runtime": "^6.22.0", "babel-template": "^6.24.1"}, "description": "Collection of helper functions used by Babel transforms.", "homepage": "https://babeljs.io/", "license": "MIT", "main": "lib/index.js", "name": "babel-helpers", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-helpers"}, "version": "6.24.1"}