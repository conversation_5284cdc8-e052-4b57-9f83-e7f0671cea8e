<template>
	<view class="merchant-record-container" :style="colorStyle">
		<!-- 导航栏 -->
		
		<view class="navbar" :style="{ paddingTop: statusBarHeight }">
			<view class="navbar-content">
				<view class="navbar-left" @click="goBack">
					<text class="iconfont icon-fanhui"></text>
				</view>
				<view class="navbar-center">
					<text class="navbar-title">申请记录</text>
				</view>
				<view class="navbar-right"></view>
			</view>
		</view>
		
		

		<!-- 内容区域 -->
		<view class="content">
			<view class="record-list" v-if="recordList.length > 0">
				<view 
					class="record-item" 
					v-for="(item, index) in recordList" 
					:key="index"
					@click="goToDetail(item)"
				>
					<view class="record-header">
						<text class="merchant-name">{{ item.store_name }}</text>
						<view class="status-tag" :class="getStatusClass(item.status)">
							<text class="status-text">{{ getStatusText(item.status) }}</text>
						</view>
					</view>
					<view class="record-info">
						<text class="info-item">联系人：{{ item.name }}</text>
						<text class="info-item">手机号：{{ item.phone }}</text>
						<text class="info-item">申请时间：{{ item.add_time }}</text>
					</view>
					<view class="record-footer" v-if="item.status === 2 && item.refusal_reason">
						<text class="refusal-reason">拒绝原因：{{ item.refusal_reason }}</text>
					</view>
				</view>
			</view>

			<!-- 空状态 -->
			<view class="empty-state" v-else-if="!loading">
				<image class="empty-icon" src="/static/images/empty.png"></image>
				<text class="empty-text">暂无申请记录</text>
				<button class="apply-btn" @click="goToApply">立即申请</button>
			</view>

			<!-- 加载状态 -->
			<view class="loading-state" v-if="loading">
				<text class="loading-icon iconfont icon-jiazai"></text>
				<text class="loading-text">加载中...</text>
			</view>
		</view>
	</view>
</template>

<script>
import { getMerchantRecords } from '@/api/merchant.js';
import { mapGetters } from "vuex";
import colors from "@/mixins/color";

export default {
	mixins: [colors],
	data() {
		return {
			statusBarHeight: uni.getSystemInfoSync().statusBarHeight + 'px',
			recordList: [],
			loading: false
		};
	},
	computed: {
		...mapGetters(['isLogin'])
	},
	onLoad() {
		this.loadRecords();
	},
	onPullDownRefresh() {
		this.loadRecords().then(() => {
			uni.stopPullDownRefresh();
		});
	},
	methods: {
		// 加载记录
		async loadRecords() {
			this.loading = true;
			try {
				const res = await getMerchantRecords();
				this.recordList = res.data.list || [];
			} catch (error) {
				console.error('加载记录失败:', error);
				this.$util.Tips({ title: '加载失败' });
			} finally {
				this.loading = false;
			}
		},

		// 返回上一页
		goBack() {
			uni.navigateBack();
		},

		// 跳转到申请页面
		goToApply() {
			uni.navigateTo({
				url: '/pages/annex/merchant_apply/index'
			});
		},

		// 跳转到详情
		goToDetail(item) {
			uni.navigateTo({
				url: `/pages/annex/merchant_apply/index?id=${item.id}`
			});
		},

		// 获取状态样式类
		getStatusClass(status) {
			const statusMap = {
				'-1': 'pending',
				'0': 'submitted',
				'1': 'approved',
				'2': 'rejected'
			};
			return statusMap[status] || 'pending';
		},

		// 获取状态文本
		getStatusText(status) {
			const statusMap = {
				'-1': '申请中',
				'0': '已提交,待审核',
				'1': '已通过',
				'2': '已拒绝'
			};
			return statusMap[status] || '未知';
		},

		// 格式化时间
		formatTime(time) {
			if (!time) return '';
			const date = new Date(time);
			return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
		}
	}
}
</script>

<style lang="scss" scoped>
.merchant-record-container {
	min-height: 100vh;
	background: #F8F9FA;
}

/* 导航栏 */
.navbar {
	background: #FFFFFF;
	border-bottom: 1rpx solid #F0F0F0;
	
	.navbar-content {
		height: 88rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 32rpx;
		
		.navbar-left {
			width: 80rpx;
			height: 60rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			
			.iconfont {
				font-size: 36rpx;
				color: #333333;
			}
		}
		
		.navbar-center {
			flex: 1;
			text-align: center;
			
			.navbar-title {
				font-size: 36rpx;
				font-weight: 600;
				color: #333333;
			}
		}
		
		.navbar-right {
			width: 80rpx;
		}
	}
}

/* 内容区域 */
.content {
	padding: 20rpx;
}

/* 记录列表 */
.record-list {
	.record-item {
		background: #FFFFFF;
		border-radius: 16rpx;
		padding: 32rpx;
		margin-bottom: 20rpx;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
		
		.record-header {
			display: flex;
			align-items: center;
			justify-content: space-between;
			margin-bottom: 24rpx;
			
			.merchant-name {
				font-size: 32rpx;
				color: #333333;
				font-weight: 600;
			}
			
			.status-tag {
				padding: 8rpx 16rpx;
				border-radius: 20rpx;
				
				&.pending {
					background: #FFF3E0;
					.status-text { color: #FF9800; }
				}
				
				&.submitted {
					background: #E3F2FD;
					.status-text { color: #2196F3; }
				}
				
				&.approved {
					background: #E8F5E8;
					.status-text { color: #4CAF50; }
				}
				
				&.rejected {
					background: #FFEBEE;
					.status-text { color: #F44336; }
				}
				
				.status-text {
					font-size: 24rpx;
					font-weight: 500;
				}
			}
		}
		
		.record-info {
			.info-item {
				display: block;
				font-size: 28rpx;
				color: #666666;
				margin-bottom: 12rpx;
				
				&:last-child {
					margin-bottom: 0;
				}
			}
		}
		
		.record-footer {
			margin-top: 20rpx;
			padding-top: 20rpx;
			border-top: 1rpx solid #F0F0F0;
			
			.refusal-reason {
				font-size: 26rpx;
				color: #F44336;
			}
		}
	}
}

/* 空状态 */
.empty-state {
	text-align: center;
	padding: 120rpx 40rpx;
	
	.empty-icon {
		width: 200rpx;
		height: 200rpx;
		margin-bottom: 40rpx;
	}
	
	.empty-text {
		font-size: 28rpx;
		color: #999999;
		margin-bottom: 60rpx;
		display: block;
	}
	
	.apply-btn {
		width: 300rpx;
		height: 80rpx;
		background: #FF6B35;
		border-radius: 40rpx;
		border: none;
		font-size: 30rpx;
		color: #FFFFFF;
		font-weight: 600;
	}
}

/* 加载状态 */
.loading-state {
	text-align: center;
	padding: 120rpx 40rpx;
	
	.loading-icon {
		font-size: 60rpx;
		color: #FF6B35;
		animation: rotate 1s linear infinite;
		display: block;
		margin-bottom: 20rpx;
	}
	
	.loading-text {
		font-size: 28rpx;
		color: #999999;
	}
}

@keyframes rotate {
	from { transform: rotate(0deg); }
	to { transform: rotate(360deg); }
}
</style>
