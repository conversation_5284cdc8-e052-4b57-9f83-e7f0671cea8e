"use strict";

exports.__esModule = true;
exports.CodeGenerator = undefined;

var _classCallCheck2 = require("babel-runtime/helpers/classCallCheck");

var _classCallCheck3 = _interopRequireDefault(_classCallCheck2);

var _possibleConstructorReturn2 = require("babel-runtime/helpers/possibleConstructorReturn");

var _possibleConstructorReturn3 = _interopRequireDefault(_possibleConstructorReturn2);

var _inherits2 = require("babel-runtime/helpers/inherits");

var _inherits3 = _interopRequireDefault(_inherits2);

exports.default = function (ast, opts, code) {
  var gen = new Generator(ast, opts, code);
  return gen.generate();
};

var _detectIndent = require("detect-indent");

var _detectIndent2 = _interopRequireDefault(_detectIndent);

var _sourceMap = require("./source-map");

var _sourceMap2 = _interopRequireDefault(_sourceMap);

var _babelMessages = require("babel-messages");

var messages = _interopRequireWildcard(_babelMessages);

var _printer = require("./printer");

var _printer2 = _interopRequireDefault(_printer);

function _interopRequireWildcard(obj) { if (obj && obj.__esModule) { return obj; } else { var newObj = {}; if (obj != null) { for (var key in obj) { if (Object.prototype.hasOwnProperty.call(obj, key)) newObj[key] = obj[key]; } } newObj.default = obj; return newObj; } }

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

var Generator = function (_Printer) {
  (0, _inherits3.default)(Generator, _Printer);

  function Generator(ast) {
    var opts = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
    var code = arguments[2];
    (0, _classCallCheck3.default)(this, Generator);

    var tokens = ast.tokens || [];
    var format = normalizeOptions(code, opts, tokens);
    var map = opts.sourceMaps ? new _sourceMap2.default(opts, code) : null;

    var _this = (0, _possibleConstructorReturn3.default)(this, _Printer.call(this, format, map, tokens));

    _this.ast = ast;
    return _this;
  }

  Generator.prototype.generate = function generate() {
    return _Printer.prototype.generate.call(this, this.ast);
  };

  return Generator;
}(_printer2.default);

function normalizeOptions(code, opts, tokens) {
  var style = "  ";
  if (code && typeof code === "string") {
    var indent = (0, _detectIndent2.default)(code).indent;
    if (indent && indent !== " ") style = indent;
  }

  var format = {
    auxiliaryCommentBefore: opts.auxiliaryCommentBefore,
    auxiliaryCommentAfter: opts.auxiliaryCommentAfter,
    shouldPrintComment: opts.shouldPrintComment,
    retainLines: opts.retainLines,
    retainFunctionParens: opts.retainFunctionParens,
    comments: opts.comments == null || opts.comments,
    compact: opts.compact,
    minified: opts.minified,
    concise: opts.concise,
    quotes: opts.quotes || findCommonStringDelimiter(code, tokens),
    jsonCompatibleStrings: opts.jsonCompatibleStrings,
    indent: {
      adjustMultilineComment: true,
      style: style,
      base: 0
    },
    flowCommaSeparator: opts.flowCommaSeparator
  };

  if (format.minified) {
    format.compact = true;

    format.shouldPrintComment = format.shouldPrintComment || function () {
      return format.comments;
    };
  } else {
    format.shouldPrintComment = format.shouldPrintComment || function (value) {
      return format.comments || value.indexOf("@license") >= 0 || value.indexOf("@preserve") >= 0;
    };
  }

  if (format.compact === "auto") {
    format.compact = code.length > 500000;

    if (format.compact) {
      console.error("[BABEL] " + messages.get("codeGeneratorDeopt", opts.filename, "500KB"));
    }
  }

  if (format.compact) {
    format.indent.adjustMultilineComment = false;
  }

  return format;
}

function findCommonStringDelimiter(code, tokens) {
  var DEFAULT_STRING_DELIMITER = "double";
  if (!code) {
    return DEFAULT_STRING_DELIMITER;
  }

  var occurrences = {
    single: 0,
    double: 0
  };

  var checked = 0;

  for (var i = 0; i < tokens.length; i++) {
    var token = tokens[i];
    if (token.type.label !== "string") continue;

    var raw = code.slice(token.start, token.end);
    if (raw[0] === "'") {
      occurrences.single++;
    } else {
      occurrences.double++;
    }

    checked++;
    if (checked >= 3) break;
  }
  if (occurrences.single > occurrences.double) {
    return "single";
  } else {
    return "double";
  }
}

var CodeGenerator = exports.CodeGenerator = function () {
  function CodeGenerator(ast, opts, code) {
    (0, _classCallCheck3.default)(this, CodeGenerator);

    this._generator = new Generator(ast, opts, code);
  }

  CodeGenerator.prototype.generate = function generate() {
    return this._generator.generate();
  };

  return CodeGenerator;
}();