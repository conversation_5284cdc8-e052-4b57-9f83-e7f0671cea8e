{"_args": [["trim-right@1.0.1", "D:\\web\\shenzhen-app"]], "_from": "trim-right@1.0.1", "_id": "trim-right@1.0.1", "_inBundle": false, "_integrity": "sha1-yy4SAwZ+DI3h9hQJS5/kVwTqYAM=", "_location": "/trim-right", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "trim-right@1.0.1", "name": "trim-right", "escapedName": "trim-right", "rawSpec": "1.0.1", "saveSpec": null, "fetchSpec": "1.0.1"}, "_requiredBy": ["/babel-generator"], "_resolved": "https://registry.npmjs.org/trim-right/-/trim-right-1.0.1.tgz", "_spec": "1.0.1", "_where": "D:\\web\\shenzhen-app", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/trim-right/issues"}, "description": "Similar to String#trim() but removes only whitespace on the right", "devDependencies": {"ava": "0.0.4"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/trim-right#readme", "keywords": ["trim", "right", "string", "str", "util", "utils", "utility", "whitespace", "space", "remove", "delete"], "license": "MIT", "name": "trim-right", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/trim-right.git"}, "scripts": {"test": "node test.js"}, "version": "1.0.1"}