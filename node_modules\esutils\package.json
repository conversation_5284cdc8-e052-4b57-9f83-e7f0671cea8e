{"_args": [["esutils@2.0.3", "D:\\web\\shenzhen-app"]], "_from": "esutils@2.0.3", "_id": "esutils@2.0.3", "_inBundle": false, "_integrity": "sha512-kVscqXk4OCp68SZ0dkgEKVi6/8ij300KBWTJq32P/dYeWTSwK41WyTxalN1eRmA5Z9UU/LX9D7FWSmV9SAYx6g==", "_location": "/esutils", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "esutils@2.0.3", "name": "esutils", "escapedName": "esutils", "rawSpec": "2.0.3", "saveSpec": null, "fetchSpec": "2.0.3"}, "_requiredBy": ["/babel-code-frame", "/babel-types"], "_resolved": "https://registry.npmjs.org/esutils/-/esutils-2.0.3.tgz", "_spec": "2.0.3", "_where": "D:\\web\\shenzhen-app", "bugs": {"url": "https://github.com/estools/esutils/issues"}, "description": "utility box for ECMAScript language tools", "devDependencies": {"chai": "~1.7.2", "coffee-script": "~1.6.3", "jshint": "2.6.3", "mocha": "~2.2.1", "regenerate": "~1.3.1", "unicode-9.0.0": "~0.7.0"}, "directories": {"lib": "./lib"}, "engines": {"node": ">=0.10.0"}, "files": ["LICENSE.BSD", "README.md", "lib"], "homepage": "https://github.com/estools/esutils", "license": "BSD-2-<PERSON><PERSON>", "main": "lib/utils.js", "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://github.com/Constellation"}], "name": "esutils", "repository": {"type": "git", "url": "git+ssh://**************/estools/esutils.git"}, "scripts": {"generate-regex": "node tools/generate-identifier-regex.js", "lint": "jshint lib/*.js", "test": "npm run-script lint && npm run-script unit-test", "unit-test": "mocha --compilers coffee:coffee-script -R spec"}, "version": "2.0.3"}