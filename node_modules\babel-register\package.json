{"_args": [["babel-register@6.26.0", "D:\\web\\shenzhen-app"]], "_from": "babel-register@6.26.0", "_id": "babel-register@6.26.0", "_inBundle": false, "_integrity": "sha1-btAhFz4vy0htestFxgCahW9kcHE=", "_location": "/babel-register", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "babel-register@6.26.0", "name": "babel-register", "escapedName": "babel-register", "rawSpec": "6.26.0", "saveSpec": null, "fetchSpec": "6.26.0"}, "_requiredBy": ["/babel-core"], "_resolved": "https://registry.npmjs.org/babel-register/-/babel-register-6.26.0.tgz", "_spec": "6.26.0", "_where": "D:\\web\\shenzhen-app", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "browser": "lib/browser.js", "dependencies": {"babel-core": "^6.26.0", "babel-runtime": "^6.26.0", "core-js": "^2.5.0", "home-or-tmp": "^2.0.0", "lodash": "^4.17.4", "mkdirp": "^0.5.1", "source-map-support": "^0.4.15"}, "description": "babel require hook", "devDependencies": {"decache": "^4.1.0"}, "license": "MIT", "main": "lib/node.js", "name": "babel-register", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-register"}, "version": "6.26.0"}