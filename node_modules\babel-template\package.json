{"_args": [["babel-template@6.26.0", "D:\\web\\shenzhen-app"]], "_from": "babel-template@6.26.0", "_id": "babel-template@6.26.0", "_inBundle": false, "_integrity": "sha1-3gPi0WOWsGn0bdn/+FIfsaDjXgI=", "_location": "/babel-template", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "babel-template@6.26.0", "name": "babel-template", "escapedName": "babel-template", "rawSpec": "6.26.0", "saveSpec": null, "fetchSpec": "6.26.0"}, "_requiredBy": ["/babel-core", "/babel-helpers"], "_resolved": "https://registry.npmjs.org/babel-template/-/babel-template-6.26.0.tgz", "_spec": "6.26.0", "_where": "D:\\web\\shenzhen-app", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "dependencies": {"babel-runtime": "^6.26.0", "babel-traverse": "^6.26.0", "babel-types": "^6.26.0", "babylon": "^6.18.0", "lodash": "^4.17.4"}, "description": "Generate an AST from a string template.", "homepage": "https://babeljs.io/", "license": "MIT", "main": "lib/index.js", "name": "babel-template", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-template"}, "version": "6.26.0"}