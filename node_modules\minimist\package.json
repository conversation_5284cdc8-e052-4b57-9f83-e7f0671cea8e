{"_args": [["minimist@1.2.5", "D:\\web\\shenzhen-app"]], "_from": "minimist@1.2.5", "_id": "minimist@1.2.5", "_inBundle": false, "_integrity": "sha512-FM9nNUYrRBAELZQT3xeZQ7fmMOBg6nWNmJKTcgsJeaLstP/UODVpGsr5OhXhhXg6f+qtJ8uiZ+PUxkDWcgIXLw==", "_location": "/minimist", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "minimist@1.2.5", "name": "minimist", "escapedName": "minimist", "rawSpec": "1.2.5", "saveSpec": null, "fetchSpec": "1.2.5"}, "_requiredBy": ["/mkdirp"], "_resolved": "https://registry.npmjs.org/minimist/-/minimist-1.2.5.tgz", "_spec": "1.2.5", "_where": "D:\\web\\shenzhen-app", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "bugs": {"url": "https://github.com/substack/minimist/issues"}, "description": "parse argument options", "devDependencies": {"covert": "^1.0.0", "tap": "~0.4.0", "tape": "^3.5.0"}, "homepage": "https://github.com/substack/minimist", "keywords": ["argv", "getopt", "parser", "optimist"], "license": "MIT", "main": "index.js", "name": "minimist", "repository": {"type": "git", "url": "git://github.com/substack/minimist.git"}, "scripts": {"coverage": "covert test/*.js", "test": "tap test/*.js"}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "ff/5", "firefox/latest", "chrome/10", "chrome/latest", "safari/5.1", "safari/latest", "opera/12"]}, "version": "1.2.5"}