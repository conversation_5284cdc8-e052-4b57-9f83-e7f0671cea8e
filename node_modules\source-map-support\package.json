{"_args": [["source-map-support@0.4.18", "D:\\web\\shenzhen-app"]], "_from": "source-map-support@0.4.18", "_id": "source-map-support@0.4.18", "_inBundle": false, "_integrity": "sha512-try0/JqxPLF9nOjvSta7tVondkP5dwgyLDjVoyMDlmjugT2lRZ1OfsrYTkCd2hkDnJTKRbO/Rl3orm8vlsUzbA==", "_location": "/source-map-support", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "source-map-support@0.4.18", "name": "source-map-support", "escapedName": "source-map-support", "rawSpec": "0.4.18", "saveSpec": null, "fetchSpec": "0.4.18"}, "_requiredBy": ["/babel-register"], "_resolved": "https://registry.npmjs.org/source-map-support/-/source-map-support-0.4.18.tgz", "_spec": "0.4.18", "_where": "D:\\web\\shenzhen-app", "bugs": {"url": "https://github.com/evanw/node-source-map-support/issues"}, "dependencies": {"source-map": "^0.5.6"}, "description": "Fixes stack traces for files with source maps", "devDependencies": {"browserify": "3.44.2", "coffee-script": "1.7.1", "http-server": "^0.8.5", "mocha": "1.18.2", "webpack": "^1.13.3"}, "homepage": "https://github.com/evanw/node-source-map-support#readme", "license": "MIT", "main": "./source-map-support.js", "name": "source-map-support", "repository": {"type": "git", "url": "git+https://github.com/evanw/node-source-map-support.git"}, "scripts": {"build": "node build.js", "prepublish": "npm run build", "serve-tests": "http-server -p 1336", "test": "mocha"}, "version": "0.4.18"}