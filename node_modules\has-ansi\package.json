{"_args": [["has-ansi@2.0.0", "D:\\web\\shenzhen-app"]], "_from": "has-ansi@2.0.0", "_id": "has-ansi@2.0.0", "_inBundle": false, "_integrity": "sha1-NPUEnOHs3ysGSa8+8k5F7TVBbZE=", "_location": "/has-ansi", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "has-ansi@2.0.0", "name": "has-ansi", "escapedName": "has-ansi", "rawSpec": "2.0.0", "saveSpec": null, "fetchSpec": "2.0.0"}, "_requiredBy": ["/chalk"], "_resolved": "https://registry.npmjs.org/has-ansi/-/has-ansi-2.0.0.tgz", "_spec": "2.0.0", "_where": "D:\\web\\shenzhen-app", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/has-ansi/issues"}, "dependencies": {"ansi-regex": "^2.0.0"}, "description": "Check if a string has ANSI escape codes", "devDependencies": {"ava": "0.0.4"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/has-ansi#readme", "keywords": ["ansi", "styles", "color", "colour", "colors", "terminal", "console", "string", "tty", "escape", "shell", "xterm", "command-line", "text", "regex", "regexp", "re", "match", "test", "find", "pattern", "has"], "license": "MIT", "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "jbnicolai.com"}], "name": "has-ansi", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/has-ansi.git"}, "scripts": {"test": "node test.js"}, "version": "2.0.0"}