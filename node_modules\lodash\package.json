{"_args": [["lodash@4.17.15", "D:\\web\\shenzhen-app"]], "_from": "lodash@4.17.15", "_id": "lodash@4.17.15", "_inBundle": false, "_integrity": "sha512-8xOcRHvCjnocdS5cpwXQXVzmmh5e5+saE2QGoeQmbKmRS6J3VQppPOIt0MnmE+4xlZoumy0GPG0D0MVIQbNA1A==", "_location": "/lodash", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "lodash@4.17.15", "name": "lodash", "escapedName": "lodash", "rawSpec": "4.17.15", "saveSpec": null, "fetchSpec": "4.17.15"}, "_requiredBy": ["/babel-core", "/babel-generator", "/babel-register", "/babel-template", "/babel-traverse", "/babel-types"], "_resolved": "https://registry.npmjs.org/lodash/-/lodash-4.17.15.tgz", "_spec": "4.17.15", "_where": "D:\\web\\shenzhen-app", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Lodash modular utilities.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "keywords": ["modules", "stdlib", "util"], "license": "MIT", "main": "lodash.js", "name": "lodash", "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash-archive/lodash-cli for testing details.\""}, "version": "4.17.15"}