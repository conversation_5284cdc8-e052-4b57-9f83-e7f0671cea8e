{"_args": [["babel-traverse@6.26.0", "D:\\web\\shenzhen-app"]], "_from": "babel-traverse@6.26.0", "_id": "babel-traverse@6.26.0", "_inBundle": false, "_integrity": "sha1-RqnL1+3MYsjlwGTi0tjQ9ANXZu4=", "_location": "/babel-traverse", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "babel-traverse@6.26.0", "name": "babel-traverse", "escapedName": "babel-traverse", "rawSpec": "6.26.0", "saveSpec": null, "fetchSpec": "6.26.0"}, "_requiredBy": ["/babel-core", "/babel-template"], "_resolved": "https://registry.npmjs.org/babel-traverse/-/babel-traverse-6.26.0.tgz", "_spec": "6.26.0", "_where": "D:\\web\\shenzhen-app", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "dependencies": {"babel-code-frame": "^6.26.0", "babel-messages": "^6.23.0", "babel-runtime": "^6.26.0", "babel-types": "^6.26.0", "babylon": "^6.18.0", "debug": "^2.6.8", "globals": "^9.18.0", "invariant": "^2.2.2", "lodash": "^4.17.4"}, "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "devDependencies": {"babel-generator": "^6.26.0"}, "homepage": "https://babeljs.io/", "license": "MIT", "main": "lib/index.js", "name": "babel-traverse", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-traverse"}, "version": "6.26.0"}