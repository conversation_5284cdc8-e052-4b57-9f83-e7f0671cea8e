{"_args": [["babel-types@6.26.0", "D:\\web\\shenzhen-app"]], "_from": "babel-types@6.26.0", "_id": "babel-types@6.26.0", "_inBundle": false, "_integrity": "sha1-o7Bz+Uq0nrb6Vc1lInozQ4BjJJc=", "_location": "/babel-types", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "babel-types@6.26.0", "name": "babel-types", "escapedName": "babel-types", "rawSpec": "6.26.0", "saveSpec": null, "fetchSpec": "6.26.0"}, "_requiredBy": ["/babel-core", "/babel-generator", "/babel-template", "/babel-traverse"], "_resolved": "https://registry.npmjs.org/babel-types/-/babel-types-6.26.0.tgz", "_spec": "6.26.0", "_where": "D:\\web\\shenzhen-app", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "dependencies": {"babel-runtime": "^6.26.0", "esutils": "^2.0.2", "lodash": "^4.17.4", "to-fast-properties": "^1.0.3"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "devDependencies": {"babel-generator": "^6.26.0", "babylon": "^6.18.0"}, "homepage": "https://babeljs.io/", "license": "MIT", "main": "lib/index.js", "name": "babel-types", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-types"}, "version": "6.26.0"}