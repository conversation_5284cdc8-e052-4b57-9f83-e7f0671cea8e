{"_args": [["is-finite@1.1.0", "D:\\web\\shenzhen-app"]], "_from": "is-finite@1.1.0", "_id": "is-finite@1.1.0", "_inBundle": false, "_integrity": "sha512-cdyMtqX/BOqqNBBiKlIVkytNHm49MtMlYyn1zxzvJKWmFMlGzm+ry5BBfYyeY9YmNKbRSo/o7OX9w9ale0wg3w==", "_location": "/is-finite", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "is-finite@1.1.0", "name": "is-finite", "escapedName": "is-finite", "rawSpec": "1.1.0", "saveSpec": null, "fetchSpec": "1.1.0"}, "_requiredBy": ["/repeating"], "_resolved": "https://registry.npmjs.org/is-finite/-/is-finite-1.1.0.tgz", "_spec": "1.1.0", "_where": "D:\\web\\shenzhen-app", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/is-finite/issues"}, "description": "ES2015 Number.isFinite() ponyfill", "devDependencies": {"ava": "^3.2.0"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "funding": "https://github.com/sponsors/sindresorhus", "homepage": "https://github.com/sindresorhus/is-finite#readme", "keywords": ["es2015", "ponyfill", "polyfill", "shim", "number", "finite", "is"], "license": "MIT", "name": "is-finite", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/is-finite.git"}, "scripts": {"test": "ava"}, "version": "1.1.0"}