{"_args": [["ansi-styles@2.2.1", "D:\\web\\shenzhen-app"]], "_from": "ansi-styles@2.2.1", "_id": "ansi-styles@2.2.1", "_inBundle": false, "_integrity": "sha1-tDLdM1i2NM914eRmQ2gkBTPB3b4=", "_location": "/ansi-styles", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "ansi-styles@2.2.1", "name": "ansi-styles", "escapedName": "ansi-styles", "rawSpec": "2.2.1", "saveSpec": null, "fetchSpec": "2.2.1"}, "_requiredBy": ["/chalk"], "_resolved": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-2.2.1.tgz", "_spec": "2.2.1", "_where": "D:\\web\\shenzhen-app", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/chalk/ansi-styles/issues"}, "description": "ANSI escape codes for styling strings in the terminal", "devDependencies": {"mocha": "*"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/chalk/ansi-styles#readme", "keywords": ["ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "license": "MIT", "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "jbnicolai.com"}], "name": "ansi-styles", "repository": {"type": "git", "url": "git+https://github.com/chalk/ansi-styles.git"}, "scripts": {"test": "mocha"}, "version": "2.2.1"}