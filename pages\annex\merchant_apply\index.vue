<template>
	<view class="merchant-apply-container" :style="colorStyle">
		<!-- 自定义导航栏 -->
		<view class="custom-navbar" :style="{ paddingTop: statusBarHeight }">
			<view class="navbar-content">
				<view class="navbar-left" @click="goBack">
					<text class="iconfont icon-fanhui"></text>
				</view>
				<view class="navbar-center">
					<text class="navbar-title">商户入驻申请</text>
				</view>
				<view class="navbar-right" @click="goToRecord">
					<text class="record-text">申请记录</text>
					<text class="iconfont icon-xiangyou"></text>
				</view>
			</view>
		</view>

		<!-- 主要内容 -->
		<view class="main-content" v-if="status == -1 && !loading">
			<!-- 头部背景和标题 -->
			<view class="header-section">
				<view class="header-bg">
					<view class="header-content">
						<view class="title-section">
							<text class="main-title">商户入驻申请</text>
							<text class="sub-title">合作共赢 共享市场</text>
						</view>
						
					</view>
				</view>
			</view>

			<!-- 表单区域 -->
			<view class="form-container">
				<view class="form-card">
					<!-- 商户名称 -->
					<view class="form-item">
						<text class="item-label">商户名称</text>
						<input type="text" :placeholder="$t('请输入商户名称')" v-model="formData.store_name"
							@input="validateForm" placeholder-class="placeholder-style" />
					</view>

					<!-- 联系人 -->
					<view class="form-item">
						<text class="item-label">联系人</text>
						<input type="text" :placeholder="$t('请输入联系人姓名')" v-model="formData.name" @input="validateForm"
							placeholder-class="placeholder-style" />
					</view>

					<!-- 联系电话 -->
					<view class="form-item">
						<text class="item-label">联系电话</text>
						<input type="text" :placeholder="$t('请输入手机号')" v-model="formData.phone" @input="validateForm"
							placeholder-class="placeholder-style" />
					</view>

					<!-- 验证码 -->
					<!-- <view class="form-item verify-item">
						<text class="item-label">验证码</text>
						<input type="text" :placeholder="$t('填写验证码')" v-model="formData.verify_code"
							@input="validateForm" placeholder-class="placeholder-style" class="verify-input" />
						<button class="verify-btn" :class="{ 'disabled': codeDisabled }" :disabled="codeDisabled"
							@click="sendVerifyCode">
							{{ codeText }}
						</button>
					</view> -->

					<!-- 商户分类 -->
					<picker
						:value="categoryIndex"
						:range="categoryList"
						range-key="class_name"
						@change="selectCategory"
					>
						<view class="form-item picker-item">
							<text class="item-label">商户分类</text>
							<text class="picker-text" :class="{ 'placeholder-style': !formData.category_name }">
								{{ formData.category_name || $t('请选择商户分类') }}
							</text>
							<text class="iconfont icon-xiangyou picker-arrow"></text>
						</view>
					</picker>

					<!-- 店铺类型 -->
					<picker
						:value="storeTypeIndex"
						:range="storeTypeList"
						range-key="type_name"
						@change="selectStoreType"
					>
						<view class="form-item picker-item">
							<text class="item-label">店铺类型</text>
							<text class="picker-text" :class="{ 'placeholder-style': !formData.store_type_name }">
								{{ formData.store_type_name || $t('请选择店铺类型') }}
							</text>
							<text class="iconfont icon-xiangyou picker-arrow"></text>
						</view>
					</picker>

					<!-- 店铺logo上传 -->
					<view class="form-item logo-upload-item">
						<text class="item-label">店铺Logo</text>
						<view class="logo-upload-container">
							<view class="logo-preview" v-if="logoImage" @tap="previewLogo">
								<image :src="logoImage" mode="aspectFill"></image>
								<text class="iconfont icon-guanbi1 delete-logo-btn" @tap.stop="deleteLogo"></text>
							</view>
							<view class="logo-upload-btn" v-else @tap="uploadLogo">
								<text class="iconfont icon-icon25201 upload-icon"></text>
								<text class="upload-text">上传Logo</text>
								<text class="upload-desc">建议尺寸：200x200像素</text>
							</view>
						</view>
					</view>

					<!-- 营业执照上传 -->
					<view class="form-item upload-item">
						<view class="upload-title">
							<text class="upload-label">请上传营业执照及行业相关资质证明图片</text>
							<text class="upload-desc">(图片最多可上传9张,图片格式支持JPG、PNG、JPEG)</text>
						</view>
						<view class="upload-container">
							<view class="image-item" v-for="(item, index) in images" :key="index"
								@tap="previewImage(index)">
								<image :src="item" mode="aspectFill"></image>
								<text class="iconfont icon-guanbi1 delete-btn" @tap.stop="deleteImage(index)"></text>
							</view>
							<view class="upload-btn" v-if="images.length < 10" @tap="uploadImage">
								<text class="iconfont icon-icon25201 upload-icon"></text>
								<text class="upload-text">上传图片</text>
							</view>
						</view>
					</view>

					<!-- 协议勾选 -->
					<view class="form-item agreement-item">
						<checkbox-group @change="changeAgreement">
							<checkbox class="agreement-checkbox" :checked="isAgree" />
						</checkbox-group>
						<text class="agreement-text">已阅读并同意</text>
						<text class="agreement-link" @click="showAgreement">《入驻协议》</text>
					</view>

					<!-- 提交按钮 -->
					<button class="submit-btn" :class="{ 'active': canSubmit }" :disabled="!canSubmit"
						@click="submitForm">
						提交申请
					</button>
				</view>
			</view>
		</view>

		<!-- 成功状态 -->
		<view class="success-container" v-else-if="status == 0 || status == 1">
			<view class="success-content">
				<image class="success-icon" src="../static/success.png"></image>
				<text class="success-title" v-if="status == 0">恭喜，您的资料提交成功！</text>
				<text class="success-title" v-else>恭喜，您的资料通过审核！</text>
				<button class="home-btn" @click="goHome">返回首页</button>
			</view>
		</view>

		<!-- 失败状态 -->
		<view class="fail-container" v-else-if="status == 2">
			<view class="fail-content">
				<image class="fail-icon" src="../static/error.png"></image>
				<text class="fail-title">您的申请未通过！</text>
				<text class="fail-reason" v-if="refusal_reason">{{ refusal_reason }}</text>
				<button class="retry-btn" @click="retryApply">重新申请</button>
				<button class="home-btn" @click="goHome">返回首页</button>
			</view>
		</view>

		<!-- 加载状态 -->
		<view class="loading-container" v-if="loading">
			<text class="loading-icon iconfont icon-jiazai"></text>
		</view>



		<!-- 协议弹窗 -->
		<view class="agreement-modal" v-if="showAgreementModal" @click="hideAgreement">
			<view class="agreement-content" @click.stop>
				<view class="agreement-header">
					<text class="agreement-title">商户入驻协议</text>
					<text class="iconfont icon-cha close-btn" @click="hideAgreement"></text>
				</view>
				<scroll-view class="agreement-body" scroll-y>
					<jyf-parser :html="agreementContent" ref="article" :tag-style="tagStyle"></jyf-parser>
				</scroll-view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		merchantApply,
		getMerchantApplyInfo,
		getMerchantCategories,
		getStoreTypes,
		getMerchantAgreement
	} from '@/api/merchant.js';
	import {
		getCodeApi,
		registerVerify
	} from '@/api/user.js';
	import {
		toLogin
	} from '@/libs/login.js';
	import {
		mapGetters
	} from "vuex";
	import parser from "@/components/jyf-parser/jyf-parser";
	import colors from "@/mixins/color";
	import sendVerifyCode from "@/mixins/SendVerifyCode";

	export default {
		components: {
			"jyf-parser": parser
		},
		mixins: [colors, sendVerifyCode],
		data() {
			return {
				statusBarHeight: uni.getSystemInfoSync().statusBarHeight + 'px',
				loading: false,
				status: -1, // -1: 申请中, 0: 已提交, 1: 已通过, 2: 已拒绝
				formData: {
					store_name: '',
					name: '',
					phone: '',
					verify_code: '',
					class_id: '',
					category_name: '',
					type_id: '',
					store_type_name: ''
				},
				images: [],
				logoImage: '', // 店铺logo图片
				uploading: false, // 上传状态标识
				isAgree: false,
				canSubmit: false,

				// 验证码相关
				codeDisabled: false,
				codeText: '获取验证码',
				keyCode: '',

				// 选择器相关
				categoryList: [],
				storeTypeList: [],
				categoryIndex: 0,
				storeTypeIndex: 0,

				// 协议相关
				showAgreementModal: false,
				agreementContent: '',

				// 其他
				refusal_reason: '',
				tagStyle: {
					img: 'width:100%;display:block;',
					table: 'width:100%',
					video: 'width:100%'
				}
			};
		},
		computed: {
			...mapGetters(['isLogin'])
		},
		onLoad() {
			if (this.isLogin) {
				this.initData();
			} else {
				toLogin();
			}
		},
		methods: {
			// 初始化数据
			async initData() {
				this.loading = true;
				try {
					await Promise.all([
						this.getMerchantApplyInfo(),
						this.loadCategories(),
						this.loadStoreTypes()
					]);
					// 初始化完成后进行一次表单验证
					this.$nextTick(() => {
						this.validateForm();
					});
				} catch (error) {
					// 初始化数据失败
				} finally {
					this.loading = false;
				}
			},

			// 获取申请信息
			async getMerchantApplyInfo() {
				try {
					const res = await getMerchantApplyInfo();
					if (res.data.status !== -1) {
						this.status = res.data.status;
						Object.keys(this.formData).forEach(key => {
							if (res.data[key] !== undefined) {
								this.formData[key] = res.data[key];
							}
						});
						if (res.data.images) {
							this.images = res.data.images;
						}
						if (res.data.logo) {
							this.logoImage = res.data.logo;
						}
						if (res.data.refusal_reason) {
							this.refusal_reason = res.data.refusal_reason;
						}
					}
				} catch (error) {
					// 获取申请信息失败
				}
			},

			// 加载商户分类
			async loadCategories() {
				try {
					const res = await getMerchantCategories();
					this.categoryList = res.data.list || [];
				} catch (error) {
					// 添加模拟数据用于测试，使用与真实API一致的数据结构
					this.categoryList = [
						{ id: 1, class_name: '百货食品', status: 1, status_text: '启用' },
						{ id: 2, class_name: '服装鞋帽', status: 1, status_text: '启用' },
						{ id: 3, class_name: '数码电器', status: 1, status_text: '启用' },
						{ id: 4, class_name: '美容护理', status: 1, status_text: '启用' },
						{ id: 5, class_name: '生活服务', status: 1, status_text: '启用' }
					];
				}
			},

			// 加载店铺类型
			async loadStoreTypes() {
				try {
					const res = await getStoreTypes();
					this.storeTypeList = res.data.list || [];
				} catch (error) {
					// 添加模拟数据用于测试，使用与真实API一致的数据结构
					this.storeTypeList = [
						{ id: 1, type_name: '专营店', status: 1, status_text: '启用', merchant_count: 0 },
						{ id: 2, type_name: '旗舰店', status: 1, status_text: '启用', merchant_count: 0 },
						{ id: 3, type_name: '普通店', status: 1, status_text: '启用', merchant_count: 0 },
						{ id: 4, type_name: '个人工作室', status: 1, status_text: '启用', merchant_count: 0 }
					];
				}
			},

			// 返回上一页
			goBack() {
				uni.navigateBack();
			},

			// 跳转到申请记录
			goToRecord() {
				uni.navigateTo({
					url: '/pages/annex/merchant_record/index'
				});
			},

			// 返回首页
			goHome() {
				uni.switchTab({
					url: '/pages/index/index'
				});
			},

			// 重新申请
			retryApply() {
				this.status = -1;
				this.refusal_reason = '';
			},

			// 表单验证
			validateForm() {
				const {
					store_name,
					name,
					phone,
					class_id,
					type_id
				} = this.formData;

				// 由于验证码字段被注释掉了，暂时不验证验证码
				const isValid = store_name && name && phone &&
					class_id && type_id && this.images.length > 0 && this.isAgree;

				this.canSubmit = isValid;
				// 注意：logoImage是可选的，不作为必填项
			},

			// 发送验证码
			async sendVerifyCode() {
				if (!this.formData.phone) {
					return this.$util.Tips({
						title: '请输入手机号'
					});
				}
				if (!/^1[3-9]\d{9}$/.test(this.formData.phone)) {
					return this.$util.Tips({
						title: '请输入正确的手机号码'
					});
				}

				try {
					this.codeDisabled = true;
					const keyRes = await getCodeApi();
					this.keyCode = keyRes.data.key;

					await registerVerify({
						phone: this.formData.phone,
						key: this.keyCode,
						type: 'merchant'
					});

					this.$util.Tips({
						title: '验证码发送成功'
					});
					this.startCountdown();
				} catch (error) {
					this.codeDisabled = false;
					this.$util.Tips({
						title: error.message || '发送失败'
					});
				}
			},

			// 开始倒计时
			startCountdown() {
				let count = 60;
				this.codeText = `${count}s`;
				const timer = setInterval(() => {
					count--;
					if (count <= 0) {
						clearInterval(timer);
						this.codeDisabled = false;
						this.codeText = '重新获取';
					} else {
						this.codeText = `${count}s`;
					}
				}, 1000);
			},



			// 选择分类
			selectCategory(e) {
				const index = e.detail.value;
				this.categoryIndex = index;
				if (this.categoryList[index]) {
					this.formData.class_id = this.categoryList[index].id;
					this.formData.category_name = this.categoryList[index].class_name;
				}
				this.validateForm();
			},

			// 选择店铺类型
			selectStoreType(e) {
				const index = e.detail.value;
				this.storeTypeIndex = index;
				if (this.storeTypeList[index]) {
					this.formData.type_id = this.storeTypeList[index].id;
					this.formData.store_type_name = this.storeTypeList[index].type_name;
				}
				this.validateForm();
			},

			// 上传店铺logo
			uploadLogo() {
				console.log('点击上传Logo');
				// 防止重复点击
				if (this.uploading) {
					console.log('正在上传中，跳过');
					return;
				}

				const that = this;
				console.log('开始选择图片');
				uni.chooseImage({
					count: 1,
					sizeType: ['compressed'],
					sourceType: ['album', 'camera'],
					success: (res) => {
						console.log('选择图片成功:', res);
						if (res.tempFilePaths && res.tempFilePaths.length > 0) {
							that.uploading = true;
							const tempPath = res.tempFilePaths[0];
							console.log('开始上传图片:', tempPath);
							// 直接使用 uploadImgs 方法，避免重复选择图片
							that.$util.uploadImgs('upload/image', tempPath, (uploadRes) => {
								console.log('上传回调:', uploadRes);
								that.uploading = false;
								if (uploadRes && uploadRes.data && uploadRes.data.url) {
									that.logoImage = uploadRes.data.url;
									that.validateForm();
									console.log('Logo上传成功，URL:', that.logoImage);
									that.$util.Tips({
										title: 'Logo上传成功'
									});
								} else {
									console.log('上传失败，响应:', uploadRes);
									that.$util.Tips({
										title: 'Logo上传失败，请重试'
									});
								}
							}, (error) => {
								console.log('上传失败:', error);
								that.uploading = false;
								that.$util.Tips({
									title: 'Logo上传失败，请重试'
								});
							});
						} else {
							console.log('没有选择到图片');
						}
					},
					fail: (err) => {
						console.log('选择图片失败:', err);
						if (err.errMsg && !err.errMsg.includes('cancel')) {
							that.$util.Tips({
								title: '选择图片失败'
							});
						}
					}
				});
			},

			// 删除店铺logo
			deleteLogo() {
				this.logoImage = '';
				this.validateForm();
			},

			// 预览店铺logo
			previewLogo() {
				uni.previewImage({
					current: this.logoImage,
					urls: [this.logoImage]
				});
			},

			// 上传图片
			uploadImage() {
				const that = this;
				const maxCount = 10 - this.images.length;
				if (maxCount <= 0) {
					this.$util.Tips({
						title: '最多只能上传10张图片'
					});
					return;
				}

				uni.chooseImage({
					count: maxCount,
					sizeType: ['compressed'],
					sourceType: ['album', 'camera'],
					success: (res) => {
						if (res.tempFilePaths && res.tempFilePaths.length > 0) {
							res.tempFilePaths.forEach(tempPath => {
								// 直接使用 uploadImgs 方法，避免重复选择图片
								that.$util.uploadImgs('upload/image', tempPath, (uploadRes) => {
									if (uploadRes && uploadRes.data && uploadRes.data.url) {
										that.images.push(uploadRes.data.url);
										that.validateForm();
									} else {
										that.$util.Tips({
											title: '图片上传失败，请重试'
										});
									}
								}, (error) => {
									that.$util.Tips({
										title: '图片上传失败，请重试'
									});
								});
							});
						}
					},
					fail: (err) => {
						if (err.errMsg && !err.errMsg.includes('cancel')) {
							that.$util.Tips({
								title: '选择图片失败'
							});
						}
					}
				});
			},

			// 删除图片
			deleteImage(index) {
				this.images.splice(index, 1);
				this.validateForm();
			},

			// 预览图片
			previewImage(index) {
				uni.previewImage({
					current: this.images[index],
					urls: this.images
				});
			},

			// 协议勾选
			changeAgreement() {
				this.isAgree = !this.isAgree;
				this.validateForm();
			},

			// 显示协议
			async showAgreement() {
				try {
					const res = await getMerchantAgreement();
					this.agreementContent = res.data.content || '';
					this.showAgreementModal = true;
				} catch (error) {
					this.$util.Tips({
						title: '获取协议失败'
					});
				}
			},

			// 隐藏协议
			hideAgreement() {
				this.showAgreementModal = false;
			},

			// 提交表单
			async submitForm() {
				if (!this.canSubmit) return;

				try {
					this.loading = true;
					const submitData = {
						...this.formData,
						zizhi_imgs: this.images,
						door_header_img: this.logoImage // 包含店铺logo
					};

					const res = await merchantApply(submitData);
					if (res.status === 200) {
						this.$util.Tips({
							title: res.msg ||'提交成功！请等待审核'
						});
						setTimeout(() => {
							this.getMerchantApplyInfo();
						}, 1000);
					}
				} catch (error) {
					this.$util.Tips({
						title: error || '提交失败'
					});
				} finally {
					this.loading = false;
				}
			}
		}
	}
</script>

<style lang="scss" scoped>
	.merchant-apply-container {
		min-height: 100vh;
		background: linear-gradient(180deg, #FF6B35 0%, #FF8A65 100%);
	}

	/* 自定义导航栏 */
	.custom-navbar {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		z-index: 999;
		background: transparent;

		.navbar-content {
			height: 88rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 0 32rpx;

			.navbar-left {
				width: 80rpx;
				height: 60rpx;
				display: flex;
				align-items: center;
				justify-content: center;

				.iconfont {
					font-size: 36rpx;
					color: #FFFFFF;
				}
			}

			.navbar-center {
				flex: 1;
				text-align: center;

				.navbar-title {
					font-size: 36rpx;
					font-weight: 600;
					color: #FFFFFF;
				}
			}

			.navbar-right {
				display: flex;
				align-items: center;

				.record-text {
					font-size: 28rpx;
					color: #FFFFFF;
					margin-right: 8rpx;
				}

				.iconfont {
					font-size: 24rpx;
					color: #FFFFFF;
				}
			}
		}
	}

	/* 主要内容 */
	.main-content {
		padding-top: 88rpx;
	}

	/* 头部区域 */
	.header-section {
		padding: 40rpx 32rpx 60rpx;

		.header-content {
			display: flex;
			align-items: center;
			justify-content: space-between;

			.title-section {
				.main-title {
					font-size: 48rpx;
					font-weight: bold;
					color: #FFFFFF;
					display: block;
					margin-bottom: 16rpx;
				}

				.sub-title {
					font-size: 28rpx;
					color: #FFFFFF;
					opacity: 0.9;
				}
			}

			.join-btn-container {
				.join-btn {
					width: 160rpx;
					height: 64rpx;
					background: #FFFFFF;
					border-radius: 32rpx;
					display: flex;
					align-items: center;
					justify-content: center;

					.join-text {
						font-size: 28rpx;
						color: #FF6B35;
						font-weight: 600;
					}
				}
			}
		}
	}

	/* 表单容器 */
	.form-container {
		padding: 0 32rpx;

		.form-card {
			background: #FFFFFF;
			border-radius: 24rpx;
			padding: 40rpx 32rpx;
			margin-bottom: 40rpx;
		}
	}

	/* 表单项 */
	.form-item {
		margin-bottom: 40rpx;

		&:last-child {
			margin-bottom: 0;
		}

		.item-label {
			font-size: 32rpx;
			color: #333333;
			font-weight: 600;
			display: block;
			margin-bottom: 20rpx;
		}

		input {
			width: 100%;
			height: 88rpx;
			background: #F8F9FA;
			border-radius: 12rpx;
			padding: 0 24rpx;
			font-size: 30rpx;
			color: #333333;
			border: none;

			&.verify-input {
				width: calc(100% - 200rpx);
			}
		}

		.placeholder-style {
			color: #CCCCCC;
		}
	}

	/* 验证码项 */
	.verify-item {
		display: flex;
		align-items: flex-end;
		flex-wrap: wrap;

		.item-label {
			width: 100%;
			margin-bottom: 20rpx;
		}

		.verify-btn {
			width: 180rpx;
			height: 88rpx;
			background: #FF6B35;
			border-radius: 12rpx;
			border: none;
			font-size: 28rpx;
			color: #FFFFFF;
			margin-left: 20rpx;
			line-height: 88rpx;

			&.disabled {
				background: #CCCCCC;
			}
		}
	}

	/* 选择器项 */
	.picker-item {
		display: flex;
		align-items: center;
		background: #F8F9FA;
		border-radius: 12rpx;
		padding: 24rpx;
		min-height: 88rpx;

		.item-label {
			font-size: 32rpx;
			color: #333333;
			font-weight: 600;
			margin-bottom: 0;
			margin-right: 24rpx;
			min-width: 160rpx;
		}

		.picker-text {
			flex: 1;
			font-size: 30rpx;
			color: #333333;

			&.placeholder-style {
				color: #CCCCCC;
			}
		}

		.picker-arrow {
			font-size: 24rpx;
			color: #CCCCCC;
		}
	}

	/* Logo上传项 */
	.logo-upload-item {
		.logo-upload-container {
			.logo-preview {
				position: relative;
				width: 200rpx;
				height: 200rpx;
				border-radius: 12rpx;
				overflow: hidden;
				border: 2rpx solid #F0F0F0;

				image {
					width: 100%;
					height: 100%;
				}

				.delete-logo-btn {
					position: absolute;
					top: 8rpx;
					right: 8rpx;
					width: 32rpx;
					height: 32rpx;
					background: rgba(0, 0, 0, 0.6);
					border-radius: 50%;
					display: flex;
					align-items: center;
					justify-content: center;
					font-size: 20rpx;
					color: #FFFFFF;
				}
			}

			.logo-upload-btn {
				width: 200rpx;
				height: 200rpx;
				background: #F8F9FA;
				border: 2rpx dashed #CCCCCC;
				border-radius: 12rpx;
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;

				.upload-icon {
					font-size: 48rpx;
					color: #CCCCCC;
					margin-bottom: 8rpx;
				}

				.upload-text {
					font-size: 28rpx;
					color: #333333;
					margin-bottom: 4rpx;
				}

				.upload-desc {
					font-size: 24rpx;
					color: #999999;
					text-align: center;
				}
			}
		}
	}

	/* 上传项 */
	.upload-item {
		.upload-title {
			margin-bottom: 24rpx;

			.upload-label {
				font-size: 32rpx;
				color: #333333;
				font-weight: 600;
				display: block;
				margin-bottom: 8rpx;
			}

			.upload-desc {
				font-size: 24rpx;
				color: #999999;
			}
		}

		.upload-container {
			display: flex;
			flex-wrap: wrap;
			gap: 20rpx;

			.image-item {
				position: relative;
				width: 160rpx;
				height: 160rpx;
				border-radius: 12rpx;
				overflow: hidden;

				image {
					width: 100%;
					height: 100%;
				}

				.delete-btn {
					position: absolute;
					top: 8rpx;
					right: 8rpx;
					width: 32rpx;
					height: 32rpx;
					background: rgba(0, 0, 0, 0.6);
					border-radius: 50%;
					display: flex;
					align-items: center;
					justify-content: center;
					font-size: 20rpx;
					color: #FFFFFF;
				}
			}

			.upload-btn {
				width: 160rpx;
				height: 160rpx;
				background: #F8F9FA;
				border: 2rpx dashed #CCCCCC;
				border-radius: 12rpx;
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;

				.upload-icon {
					font-size: 48rpx;
					color: #CCCCCC;
					margin-bottom: 8rpx;
				}

				.upload-text {
					font-size: 24rpx;
					color: #CCCCCC;
				}
			}
		}
	}

	/* 协议项 */
	.agreement-item {
		display: flex;
		align-items: center;
		margin-bottom: 60rpx !important;

		.agreement-checkbox {
			margin-right: 16rpx;
		}

		.agreement-text {
			font-size: 28rpx;
			color: #666666;
			margin-right: 8rpx;
		}

		.agreement-link {
			font-size: 28rpx;
			color: #FF6B35;
			text-decoration: underline;
		}
	}

	/* 提交按钮 */
	.submit-btn {
		width: 100%;
		height: 88rpx;
		line-height: 88rpx;
		background: #CCCCCC;
		border-radius: 44rpx;
		border: none;
		font-size: 32rpx;
		color: #FFFFFF;
		font-weight: 600;

		&.active {
			background: #FF6B35;
		}
	}

	/* 成功状态 */
	.success-container {
		display: flex;
		align-items: center;
		justify-content: center;
		min-height: 100vh;
		padding: 40rpx;

		.success-content {
			text-align: center;

			.success-icon {
				width: 200rpx;
				height: 200rpx;
				margin-bottom: 40rpx;
			}

			.success-title {
				font-size: 36rpx;
				color: #FFFFFF;
				font-weight: 600;
				margin-bottom: 60rpx;
				display: block;
			}

			.home-btn {
				width: 300rpx;
				height: 80rpx;
				background: #FFFFFF;
				border-radius: 40rpx;
				border: none;
				font-size: 30rpx;
				color: #FF6B35;
				text-align:center;
				line-height: 80rpx;
				font-weight: 600;
				margin: 0px auto;
			}
		}
	}

	/* 失败状态 */
	.fail-container {
		display: flex;
		align-items: center;
		justify-content: center;
		min-height: 100vh;
		padding: 40rpx;

		.fail-content {
			text-align: center;

			.fail-icon {
				width: 200rpx;
				height: 200rpx;
				margin-bottom: 40rpx;
			}

			.fail-title {
				font-size: 36rpx;
				color: #FFFFFF;
				font-weight: 600;
				margin-bottom: 20rpx;
				display: block;
			}

			.fail-reason {
				font-size: 28rpx;
				color: #FFFFFF;
				opacity: 0.8;
				margin-bottom: 60rpx;
				display: block;
			}

			.retry-btn {
				width: 300rpx;
				height: 80rpx;
				line-height: 80rpx;
				background: #FFFFFF;
				border-radius: 40rpx;
				border: none;
				font-size: 30rpx;
				color: #FF6B35;
				font-weight: 600;
				margin-bottom: 20rpx;
			}

			.home-btn {
				width: 300rpx;
				height: 80rpx;
				line-height: 80rpx;
				background: transparent;
				border: 2rpx solid #FFFFFF;
				border-radius: 40rpx;
				font-size: 30rpx;
				color: #FFFFFF;
				font-weight: 600;
			}
		}
	}

	/* 加载状态 */
	.loading-container {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(0, 0, 0, 0.5);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 9999;

		.loading-icon {
			font-size: 60rpx;
			color: #FFFFFF;
			animation: rotate 1s linear infinite;
		}
	}

	/* 协议弹窗 */
	.agreement-modal {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(0, 0, 0, 0.5);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 9999;
		padding: 40rpx;

		.agreement-content {
			width: 100%;
			max-width: 600rpx;
			max-height: 80vh;
			background: #FFFFFF;
			border-radius: 24rpx;
			overflow: hidden;

			.agreement-header {
				display: flex;
				align-items: center;
				justify-content: space-between;
				padding: 32rpx;
				border-bottom: 1rpx solid #F0F0F0;

				.agreement-title {
					font-size: 32rpx;
					color: #333333;
					font-weight: 600;
				}

				.close-btn {
					font-size: 32rpx;
					color: #999999;
				}
			}

			.agreement-body {
				max-height: 60vh;
				padding: 32rpx;
			}
		}
	}

	/* 动画 */
	@keyframes rotate {
		from {
			transform: rotate(0deg);
		}

		to {
			transform: rotate(360deg);
		}
	}

	/* 响应式适配 */
	@media screen and (max-width: 750rpx) {
		.header-content {
			flex-direction: column;
			align-items: flex-start !important;

			.title-section {
				margin-bottom: 24rpx;
			}
		}

		.upload-container {

			.image-item,
			.upload-btn {
				width: 140rpx !important;
				height: 140rpx !important;
			}
		}
	}
</style>