{"_from": "@vue/shared@3.5.16", "_id": "@vue/shared@3.5.16", "_inBundle": false, "_integrity": "sha512-c/0fWy3Jw6Z8L9FmTyYfkpM5zklnqqa9+a6dz3DvONRKW2NEbh46BP0FHuLFSWi2TnQEtp91Z6zOWNrU6QiyPg==", "_location": "/@vue/shared", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@vue/shared@3.5.16", "name": "@vue/shared", "escapedName": "@vue%2fshared", "scope": "@vue", "rawSpec": "3.5.16", "saveSpec": null, "fetchSpec": "3.5.16"}, "_requiredBy": ["/@vue/compiler-core", "/@vue/compiler-dom", "/@vue/compiler-sfc", "/@vue/compiler-ssr", "/@vue/reactivity", "/@vue/runtime-core", "/@vue/runtime-dom", "/@vue/server-renderer", "/vue"], "_resolved": "https://registry.npmmirror.com/@vue/shared/-/shared-3.5.16.tgz", "_shasum": "d5ea7671182742192938a4b4cbf86ef12bef7418", "_spec": "@vue/shared@3.5.16", "_where": "D:\\web\\shenzhen-app\\node_modules\\vue", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/vuejs/core/issues"}, "buildOptions": {"formats": ["esm-bundler", "cjs"]}, "bundleDependencies": false, "deprecated": false, "description": "internal utils shared across @vue packages", "exports": {".": {"types": "./dist/shared.d.ts", "node": {"production": "./dist/shared.cjs.prod.js", "development": "./dist/shared.cjs.js", "default": "./index.js"}, "module": "./dist/shared.esm-bundler.js", "import": "./dist/shared.esm-bundler.js", "require": "./index.js"}, "./*": "./*"}, "files": ["index.js", "dist"], "homepage": "https://github.com/vuejs/core/tree/main/packages/shared#readme", "keywords": ["vue"], "license": "MIT", "main": "index.js", "module": "dist/shared.esm-bundler.js", "name": "@vue/shared", "repository": {"type": "git", "url": "git+https://github.com/vuejs/core.git", "directory": "packages/shared"}, "sideEffects": false, "types": "dist/shared.d.ts", "version": "3.5.16"}