{"_args": [["globals@9.18.0", "D:\\web\\shenzhen-app"]], "_from": "globals@9.18.0", "_id": "globals@9.18.0", "_inBundle": false, "_integrity": "sha512-S0nG3CLEQiY/ILxqtztTWH/3iRRdyBLw6KMDxnKMchrtbj2OFmehVh0WUCfW3DUrIgx/qFrJPICrq4Z4sTR9UQ==", "_location": "/globals", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "globals@9.18.0", "name": "globals", "escapedName": "globals", "rawSpec": "9.18.0", "saveSpec": null, "fetchSpec": "9.18.0"}, "_requiredBy": ["/babel-traverse"], "_resolved": "https://registry.npmjs.org/globals/-/globals-9.18.0.tgz", "_spec": "9.18.0", "_where": "D:\\web\\shenzhen-app", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/globals/issues"}, "description": "Global identifiers from different JavaScript environments", "devDependencies": {"mocha": "*"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js", "globals.json"], "homepage": "https://github.com/sindresorhus/globals#readme", "keywords": ["globals", "global", "identifiers", "variables", "vars", "j<PERSON>t", "eslint", "environments"], "license": "MIT", "name": "globals", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/globals.git"}, "scripts": {"test": "mocha"}, "version": "9.18.0"}