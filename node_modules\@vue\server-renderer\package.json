{"_from": "@vue/server-renderer@3.5.16", "_id": "@vue/server-renderer@3.5.16", "_inBundle": false, "_integrity": "sha512-BrX0qLiv/WugguGsnQUJiYOE0Fe5mZTwi6b7X/ybGB0vfrPH9z0gD/Y6WOR1sGCgX4gc25L1RYS5eYQKDMoNIg==", "_location": "/@vue/server-renderer", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@vue/server-renderer@3.5.16", "name": "@vue/server-renderer", "escapedName": "@vue%2fserver-renderer", "scope": "@vue", "rawSpec": "3.5.16", "saveSpec": null, "fetchSpec": "3.5.16"}, "_requiredBy": ["/vue"], "_resolved": "https://registry.npmmirror.com/@vue/server-renderer/-/server-renderer-3.5.16.tgz", "_shasum": "5a68cd1d423d843f74c9e6b37133850abab07c13", "_spec": "@vue/server-renderer@3.5.16", "_where": "D:\\web\\shenzhen-app\\node_modules\\vue", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/vuejs/core/issues"}, "buildOptions": {"name": "VueServerR<PERSON><PERSON>", "formats": ["esm-bundler", "esm-browser", "cjs"]}, "bundleDependencies": false, "dependencies": {"@vue/compiler-ssr": "3.5.16", "@vue/shared": "3.5.16"}, "deprecated": false, "description": "@vue/server-renderer", "exports": {".": {"types": "./dist/server-renderer.d.ts", "node": {"production": "./dist/server-renderer.cjs.prod.js", "development": "./dist/server-renderer.cjs.js", "default": "./index.js"}, "module": "./dist/server-renderer.esm-bundler.js", "import": "./dist/server-renderer.esm-bundler.js", "require": "./index.js"}, "./*": "./*"}, "files": ["index.js", "dist"], "homepage": "https://github.com/vuejs/core/tree/main/packages/server-renderer#readme", "keywords": ["vue"], "license": "MIT", "main": "index.js", "module": "dist/server-renderer.esm-bundler.js", "name": "@vue/server-renderer", "peerDependencies": {"vue": "3.5.16"}, "repository": {"type": "git", "url": "git+https://github.com/vuejs/core.git", "directory": "packages/server-renderer"}, "types": "dist/server-renderer.d.ts", "version": "3.5.16"}