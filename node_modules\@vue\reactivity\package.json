{"_from": "@vue/reactivity@3.5.16", "_id": "@vue/reactivity@3.5.16", "_inBundle": false, "_integrity": "sha512-FG5Q5ee/kxhIm1p2bykPpPwqiUBV3kFySsHEQha5BJvjXdZTUfmya7wP7zC39dFuZAcf/PD5S4Lni55vGLMhvA==", "_location": "/@vue/reactivity", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@vue/reactivity@3.5.16", "name": "@vue/reactivity", "escapedName": "@vue%2freactivity", "scope": "@vue", "rawSpec": "3.5.16", "saveSpec": null, "fetchSpec": "3.5.16"}, "_requiredBy": ["/@vue/runtime-core", "/@vue/runtime-dom"], "_resolved": "https://registry.npmmirror.com/@vue/reactivity/-/reactivity-3.5.16.tgz", "_shasum": "528c535a088b3c1b67f285f1f2211be79425b962", "_spec": "@vue/reactivity@3.5.16", "_where": "D:\\web\\shenzhen-app\\node_modules\\@vue\\runtime-dom", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/vuejs/core/issues"}, "buildOptions": {"name": "VueReactivity", "formats": ["esm-bundler", "esm-browser", "cjs", "global"]}, "bundleDependencies": false, "dependencies": {"@vue/shared": "3.5.16"}, "deprecated": false, "description": "@vue/reactivity", "exports": {".": {"types": "./dist/reactivity.d.ts", "node": {"production": "./dist/reactivity.cjs.prod.js", "development": "./dist/reactivity.cjs.js", "default": "./index.js"}, "module": "./dist/reactivity.esm-bundler.js", "import": "./dist/reactivity.esm-bundler.js", "require": "./index.js"}, "./*": "./*"}, "files": ["index.js", "dist"], "homepage": "https://github.com/vuejs/core/tree/main/packages/reactivity#readme", "jsdelivr": "dist/reactivity.global.js", "keywords": ["vue"], "license": "MIT", "main": "index.js", "module": "dist/reactivity.esm-bundler.js", "name": "@vue/reactivity", "repository": {"type": "git", "url": "git+https://github.com/vuejs/core.git", "directory": "packages/reactivity"}, "sideEffects": false, "types": "dist/reactivity.d.ts", "unpkg": "dist/reactivity.global.js", "version": "3.5.16"}