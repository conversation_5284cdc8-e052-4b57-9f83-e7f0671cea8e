{"_args": [["mkdirp@0.5.5", "D:\\web\\shenzhen-app"]], "_from": "mkdirp@0.5.5", "_id": "mkdirp@0.5.5", "_inBundle": false, "_integrity": "sha512-NKmAlESf6jMGym1++R0Ra7wvhV+wFW63FaSOFPwRahvea0gMUcGUhVeAg/0BC0wiv9ih5NYPB1Wn1UEI1/L+xQ==", "_location": "/mkdirp", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "mkdirp@0.5.5", "name": "mkdirp", "escapedName": "mkdirp", "rawSpec": "0.5.5", "saveSpec": null, "fetchSpec": "0.5.5"}, "_requiredBy": ["/babel-register"], "_resolved": "https://registry.npmjs.org/mkdirp/-/mkdirp-0.5.5.tgz", "_spec": "0.5.5", "_where": "D:\\web\\shenzhen-app", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "bin": {"mkdirp": "bin/cmd.js"}, "bugs": {"url": "https://github.com/substack/node-mkdirp/issues"}, "dependencies": {"minimist": "^1.2.5"}, "description": "Recursively mkdir, like `mkdir -p`", "devDependencies": {"mock-fs": "^3.7.0", "tap": "^5.4.2"}, "files": ["bin", "index.js"], "homepage": "https://github.com/substack/node-mkdirp#readme", "keywords": ["mkdir", "directory"], "license": "MIT", "main": "index.js", "name": "mkdirp", "publishConfig": {"tag": "legacy"}, "repository": {"type": "git", "url": "git+https://github.com/substack/node-mkdirp.git"}, "scripts": {"test": "tap test/*.js"}, "version": "0.5.5"}