{"_args": [["private@0.1.8", "D:\\web\\shenzhen-app"]], "_from": "private@0.1.8", "_id": "private@0.1.8", "_inBundle": false, "_integrity": "sha512-VvivMrbvd2nKkiG38qjULzlc+4Vx4wm/whI9pQD35YrARNnhxeiRktSOhSukRLFNlzg6Br/cJPet5J/u19r/mg==", "_location": "/private", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "private@0.1.8", "name": "private", "escapedName": "private", "rawSpec": "0.1.8", "saveSpec": null, "fetchSpec": "0.1.8"}, "_requiredBy": ["/babel-core"], "_resolved": "https://registry.npmjs.org/private/-/private-0.1.8.tgz", "_spec": "0.1.8", "_where": "D:\\web\\shenzhen-app", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/benjamn/private/issues"}, "description": "Utility for associating truly private state with any JavaScript object", "devDependencies": {"mocha": "^4.0.1"}, "engines": {"node": ">= 0.6"}, "files": ["private.js"], "homepage": "http://github.com/benjamn/private", "keywords": ["private", "access control", "access modifiers", "encapsulation", "secret", "state", "privilege", "scope", "es5"], "license": "MIT", "main": "private.js", "name": "private", "repository": {"type": "git", "url": "git://github.com/benjamn/private.git"}, "scripts": {"test": "mocha --reporter spec --full-trace test/run.js"}, "version": "0.1.8"}