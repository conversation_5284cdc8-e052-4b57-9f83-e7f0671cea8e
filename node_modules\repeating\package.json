{"_args": [["repeating@2.0.1", "D:\\web\\shenzhen-app"]], "_from": "repeating@2.0.1", "_id": "repeating@2.0.1", "_inBundle": false, "_integrity": "sha1-UhTFOpJtNVJwdSf7q0FdvAjQbdo=", "_location": "/repeating", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "repeating@2.0.1", "name": "repeating", "escapedName": "repeating", "rawSpec": "2.0.1", "saveSpec": null, "fetchSpec": "2.0.1"}, "_requiredBy": ["/detect-indent"], "_resolved": "https://registry.npmjs.org/repeating/-/repeating-2.0.1.tgz", "_spec": "2.0.1", "_where": "D:\\web\\shenzhen-app", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/repeating/issues"}, "dependencies": {"is-finite": "^1.0.0"}, "description": "Repeat a string - fast", "devDependencies": {"ava": "*", "xo": "*"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/repeating#readme", "keywords": ["repeat", "string", "repeating", "str", "text", "fill", "pad"], "license": "MIT", "name": "repeating", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/repeating.git"}, "scripts": {"test": "xo && ava"}, "version": "2.0.1"}