{"_args": [["to-fast-properties@1.0.3", "D:\\web\\shenzhen-app"]], "_from": "to-fast-properties@1.0.3", "_id": "to-fast-properties@1.0.3", "_inBundle": false, "_integrity": "sha1-uDVx+k2MJbguIxsG46MFXeTKGkc=", "_location": "/to-fast-properties", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "to-fast-properties@1.0.3", "name": "to-fast-properties", "escapedName": "to-fast-properties", "rawSpec": "1.0.3", "saveSpec": null, "fetchSpec": "1.0.3"}, "_requiredBy": ["/babel-types"], "_resolved": "https://registry.npmjs.org/to-fast-properties/-/to-fast-properties-1.0.3.tgz", "_spec": "1.0.3", "_where": "D:\\web\\shenzhen-app", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/to-fast-properties/issues"}, "description": "Force V8 to use fast properties for an object", "devDependencies": {"ava": "0.0.4"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/to-fast-properties#readme", "keywords": ["object", "obj", "properties", "props", "v8", "optimize", "fast", "convert", "mode"], "license": "MIT", "name": "to-fast-properties", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/to-fast-properties.git"}, "scripts": {"test": "node --allow-natives-syntax test.js"}, "version": "1.0.3"}