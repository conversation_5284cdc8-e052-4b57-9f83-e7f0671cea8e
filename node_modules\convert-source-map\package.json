{"_args": [["convert-source-map@1.7.0", "D:\\web\\shenzhen-app"]], "_from": "convert-source-map@1.7.0", "_id": "convert-source-map@1.7.0", "_inBundle": false, "_integrity": "sha512-4FJkXzKXEDB1snCFZlLP4gpC3JILicCpGbzG9f9G7tGqGCzETQ2hWPrcinA9oU4wtf2biUaEH5065UnMeR33oA==", "_location": "/convert-source-map", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "convert-source-map@1.7.0", "name": "convert-source-map", "escapedName": "convert-source-map", "rawSpec": "1.7.0", "saveSpec": null, "fetchSpec": "1.7.0"}, "_requiredBy": ["/babel-core"], "_resolved": "https://registry.npmjs.org/convert-source-map/-/convert-source-map-1.7.0.tgz", "_spec": "1.7.0", "_where": "D:\\web\\shenzhen-app", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://thlorenz.com"}, "browser": {"fs": false}, "bugs": {"url": "https://github.com/thlorenz/convert-source-map/issues"}, "dependencies": {"safe-buffer": "~5.1.1"}, "description": "Converts a source-map from/to  different formats and allows adding/changing properties.", "devDependencies": {"inline-source-map": "~0.6.2", "tap": "~9.0.0"}, "engine": {"node": ">=0.6"}, "files": ["index.js"], "homepage": "https://github.com/thlorenz/convert-source-map", "keywords": ["convert", "sourcemap", "source", "map", "browser", "debug"], "license": "MIT", "main": "index.js", "name": "convert-source-map", "repository": {"type": "git", "url": "git://github.com/thlorenz/convert-source-map.git"}, "scripts": {"test": "tap test/*.js --color"}, "version": "1.7.0"}