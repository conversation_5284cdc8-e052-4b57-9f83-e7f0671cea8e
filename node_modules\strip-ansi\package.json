{"_args": [["strip-ansi@3.0.1", "D:\\web\\shenzhen-app"]], "_from": "strip-ansi@3.0.1", "_id": "strip-ansi@3.0.1", "_inBundle": false, "_integrity": "sha1-ajhfuIU9lS1f8F0Oiq+UJ43GPc8=", "_location": "/strip-ansi", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "strip-ansi@3.0.1", "name": "strip-ansi", "escapedName": "strip-ansi", "rawSpec": "3.0.1", "saveSpec": null, "fetchSpec": "3.0.1"}, "_requiredBy": ["/chalk"], "_resolved": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-3.0.1.tgz", "_spec": "3.0.1", "_where": "D:\\web\\shenzhen-app", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/chalk/strip-ansi/issues"}, "dependencies": {"ansi-regex": "^2.0.0"}, "description": "Strip ANSI escape codes", "devDependencies": {"ava": "*", "xo": "*"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/chalk/strip-ansi#readme", "keywords": ["strip", "trim", "remove", "ansi", "styles", "color", "colour", "colors", "terminal", "console", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "license": "MIT", "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "jbna.nl"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "github.com/qix-"}], "name": "strip-ansi", "repository": {"type": "git", "url": "git+https://github.com/chalk/strip-ansi.git"}, "scripts": {"test": "xo && ava"}, "version": "3.0.1"}