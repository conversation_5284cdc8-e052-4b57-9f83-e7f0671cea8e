{"_from": "vue", "_id": "vue@3.5.16", "_inBundle": false, "_integrity": "sha512-rjOV2ecxMd5SiAmof2xzh2WxntRcigkX/He4YFJ6WdRvVUrbt6DxC1Iujh10XLl8xCDRDtGKMeO3D+pRQ1PP9w==", "_location": "/vue", "_phantomChildren": {}, "_requested": {"type": "tag", "registry": true, "raw": "vue", "name": "vue", "escapedName": "vue", "rawSpec": "", "saveSpec": null, "fetchSpec": "latest"}, "_requiredBy": ["#USER", "/"], "_resolved": "https://registry.npmmirror.com/vue/-/vue-3.5.16.tgz", "_shasum": "f0cde88c2688354f00ff2d77eb295c26440f8c7a", "_spec": "vue", "_where": "D:\\web\\shenzhen-app", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/vuejs/core/issues"}, "buildOptions": {"name": "<PERSON><PERSON>", "formats": ["esm-bundler", "esm-bundler-runtime", "cjs", "global", "global-runtime", "esm-browser", "esm-browser-runtime"]}, "bundleDependencies": false, "dependencies": {"@vue/compiler-dom": "3.5.16", "@vue/compiler-sfc": "3.5.16", "@vue/runtime-dom": "3.5.16", "@vue/server-renderer": "3.5.16", "@vue/shared": "3.5.16"}, "deprecated": false, "description": "The progressive JavaScript framework for building modern web UI.", "exports": {".": {"import": {"types": "./dist/vue.d.mts", "node": "./index.mjs", "default": "./dist/vue.runtime.esm-bundler.js"}, "require": {"types": "./dist/vue.d.ts", "node": {"production": "./dist/vue.cjs.prod.js", "development": "./dist/vue.cjs.js", "default": "./index.js"}, "default": "./index.js"}}, "./server-renderer": {"import": {"types": "./server-renderer/index.d.mts", "default": "./server-renderer/index.mjs"}, "require": {"types": "./server-renderer/index.d.ts", "default": "./server-renderer/index.js"}}, "./compiler-sfc": {"import": {"types": "./compiler-sfc/index.d.mts", "browser": "./compiler-sfc/index.browser.mjs", "default": "./compiler-sfc/index.mjs"}, "require": {"types": "./compiler-sfc/index.d.ts", "browser": "./compiler-sfc/index.browser.js", "default": "./compiler-sfc/index.js"}}, "./jsx-runtime": {"types": "./jsx-runtime/index.d.ts", "import": "./jsx-runtime/index.mjs", "require": "./jsx-runtime/index.js"}, "./jsx-dev-runtime": {"types": "./jsx-runtime/index.d.ts", "import": "./jsx-runtime/index.mjs", "require": "./jsx-runtime/index.js"}, "./jsx": "./jsx.d.ts", "./dist/*": "./dist/*", "./package.json": "./package.json"}, "files": ["index.js", "index.mjs", "dist", "compiler-sfc", "server-renderer", "jsx-runtime", "jsx.d.ts"], "homepage": "https://github.com/vuejs/core/tree/main/packages/vue#readme", "jsdelivr": "dist/vue.global.js", "keywords": ["vue"], "license": "MIT", "main": "index.js", "module": "dist/vue.runtime.esm-bundler.js", "name": "vue", "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "repository": {"type": "git", "url": "git+https://github.com/vuejs/core.git"}, "types": "dist/vue.d.ts", "unpkg": "dist/vue.global.js", "version": "3.5.16"}