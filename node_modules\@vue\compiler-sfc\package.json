{"_from": "@vue/compiler-sfc@3.5.16", "_id": "@vue/compiler-sfc@3.5.16", "_inBundle": false, "_integrity": "sha512-rQR6VSFNpiinDy/DVUE0vHoIDUF++6p910cgcZoaAUm3POxgNOOdS/xgoll3rNdKYTYPnnbARDCZOyZ+QSe6Pw==", "_location": "/@vue/compiler-sfc", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@vue/compiler-sfc@3.5.16", "name": "@vue/compiler-sfc", "escapedName": "@vue%2fcompiler-sfc", "scope": "@vue", "rawSpec": "3.5.16", "saveSpec": null, "fetchSpec": "3.5.16"}, "_requiredBy": ["/vue"], "_resolved": "https://registry.npmmirror.com/@vue/compiler-sfc/-/compiler-sfc-3.5.16.tgz", "_shasum": "577f7fd42a46fac8357ffed46e8fb34d32698419", "_spec": "@vue/compiler-sfc@3.5.16", "_where": "D:\\web\\shenzhen-app\\node_modules\\vue", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/vuejs/core/issues"}, "buildOptions": {"name": "VueCompilerSFC", "formats": ["cjs", "esm-browser"], "prod": false, "enableNonBrowserBranches": true}, "bundleDependencies": false, "dependencies": {"@babel/parser": "^7.27.2", "@vue/compiler-core": "3.5.16", "@vue/compiler-dom": "3.5.16", "@vue/compiler-ssr": "3.5.16", "@vue/shared": "3.5.16", "estree-walker": "^2.0.2", "magic-string": "^0.30.17", "postcss": "^8.5.3", "source-map-js": "^1.2.1"}, "deprecated": false, "description": "@vue/compiler-sfc", "devDependencies": {"@babel/types": "^7.27.1", "@vue/consolidate": "^1.0.0", "hash-sum": "^2.0.0", "lru-cache": "10.1.0", "merge-source-map": "^1.1.0", "minimatch": "~10.0.1", "postcss-modules": "^6.0.1", "postcss-selector-parser": "^7.1.0", "pug": "^3.0.3", "sass": "^1.89.0"}, "exports": {".": {"types": "./dist/compiler-sfc.d.ts", "node": "./dist/compiler-sfc.cjs.js", "module": "./dist/compiler-sfc.esm-browser.js", "import": "./dist/compiler-sfc.esm-browser.js", "require": "./dist/compiler-sfc.cjs.js"}, "./*": "./*"}, "files": ["dist"], "homepage": "https://github.com/vuejs/core/tree/main/packages/compiler-sfc#readme", "keywords": ["vue"], "license": "MIT", "main": "dist/compiler-sfc.cjs.js", "module": "dist/compiler-sfc.esm-browser.js", "name": "@vue/compiler-sfc", "repository": {"type": "git", "url": "git+https://github.com/vuejs/core.git", "directory": "packages/compiler-sfc"}, "types": "dist/compiler-sfc.d.ts", "version": "3.5.16"}