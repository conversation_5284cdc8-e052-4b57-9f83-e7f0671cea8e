{"_args": [["json5@0.5.1", "D:\\web\\shenzhen-app"]], "_from": "json5@0.5.1", "_id": "json5@0.5.1", "_inBundle": false, "_integrity": "sha1-Hq3nrMASA0rYTiOWdn6tn6VJWCE=", "_location": "/json5", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "json5@0.5.1", "name": "json5", "escapedName": "json5", "rawSpec": "0.5.1", "saveSpec": null, "fetchSpec": "0.5.1"}, "_requiredBy": ["/babel-core"], "_resolved": "https://registry.npmjs.org/json5/-/json5-0.5.1.tgz", "_spec": "0.5.1", "_where": "D:\\web\\shenzhen-app", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "bin": {"json5": "lib/cli.js"}, "bugs": {"url": "https://github.com/aseemk/json5/issues"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "jordan<PERSON><EMAIL>"}], "dependencies": {}, "description": "JSON for the ES5 era.", "devDependencies": {"gulp": "^3.9.1", "gulp-jshint": "^2.0.1", "jshint": "^2.9.3", "jshint-stylish": "^2.2.1", "mocha": "^3.1.0"}, "files": ["lib/"], "homepage": "http://json5.org/", "keywords": ["json", "es5"], "license": "MIT", "main": "lib/json5.js", "name": "json5", "repository": {"type": "git", "url": "git+https://github.com/aseemk/json5.git"}, "scripts": {"build": "node ./lib/cli.js -c package.json5", "test": "mocha --ui exports --reporter spec"}, "version": "0.5.1"}