{"_args": [["invariant@2.2.4", "D:\\web\\shenzhen-app"]], "_from": "invariant@2.2.4", "_id": "invariant@2.2.4", "_inBundle": false, "_integrity": "sha512-phJfQVBuaJM5raOpJjSfkiD6BpbCE4Ns//LaXl6wGYtUBY83nWS6Rf9tXm2e8VaK60JEjYldbPif/A2B1C2gNA==", "_location": "/invariant", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "invariant@2.2.4", "name": "invariant", "escapedName": "invariant", "rawSpec": "2.2.4", "saveSpec": null, "fetchSpec": "2.2.4"}, "_requiredBy": ["/babel-traverse"], "_resolved": "https://registry.npmjs.org/invariant/-/invariant-2.2.4.tgz", "_spec": "2.2.4", "_where": "D:\\web\\shenzhen-app", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "browser": "browser.js", "browserify": {"transform": ["loose-envify"]}, "bugs": {"url": "https://github.com/zertosh/invariant/issues"}, "dependencies": {"loose-envify": "^1.0.0"}, "description": "invariant", "devDependencies": {"browserify": "^11.0.1", "flow-bin": "^0.67.1", "tap": "^1.4.0"}, "files": ["browser.js", "invariant.js", "invariant.js.flow"], "homepage": "https://github.com/zertosh/invariant#readme", "keywords": ["test", "invariant"], "license": "MIT", "main": "invariant.js", "name": "invariant", "repository": {"type": "git", "url": "git+https://github.com/zertosh/invariant.git"}, "scripts": {"test": "NODE_ENV=production tap test/*.js && NODE_ENV=development tap test/*.js"}, "version": "2.2.4"}